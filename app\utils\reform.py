import re
import os
import logging
from typing import List, Tuple, Optional
import json
import calendar
from datetime import datetime, date

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException, DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

class MarkdownTableReformer:
    """
    Markdown表格重整器，用于从多行表头中提取元数据并整理表格结构
    """

    @log_and_reraise(logger, "MarkdownTableReformer initialization")
    def __init__(self):
        """Initialize MarkdownTableReformer with unified error handling"""
        set_operation_context("markdown_table_reformer_init")

        with error_boundary("keyword configuration", LayerType.UTILITY):
            # 关键词定义
            self.metadata_keywords = ['时间', '机构', '编制', '单位', '公司']
            self.header_keywords = ['日期', '时间', '项目', '科目', '月度', '期间', '行次', '序号']

            logger.debug("MarkdownTableReformer initialized")
        
    @handle_layer_boundary(LayerType.UTILITY, "parse_markdown_table")
    def parse_markdown_table(self, content: str) -> Tuple[List[List[str]], int, List[str]]:
        """
        解析markdown表格，返回表格数据、分割行位置和markdown标题 with unified error handling
        """
        set_operation_context("markdown_table_parsing")

        # Validate input
        with error_boundary("input validation", LayerType.UTILITY):
            if not content:
                raise ValidationException(
                    message="Empty content provided",
                    field_name="content",
                    details="Content cannot be empty or None",
                    suggested_action="Provide valid markdown content"
                )

            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__},
                    suggested_action="Provide a valid string content"
                )

        logger.debug("Parsing markdown table")

        # Parse content
        with error_boundary("content parsing", LayerType.UTILITY):
            try:
                lines = content.strip().split('\n')
                table_data = []
                separator_line = -1
                markdown_headers = []

                # 收集markdown标题行
                start_idx = 0
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line.startswith('#'):
                        markdown_headers.append(line)
                    elif line.startswith('|'):
                        start_idx = i
                        break

            except Exception as e:
                raise DataProcessingException(
                    message="Failed to parse markdown content",
                    details=f"Content parsing error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check markdown format and structure"
                )
        
        # 解析表格
        for i, line in enumerate(lines[start_idx:], start_idx):
            line = line.strip()
            if line.startswith('|') and line.endswith('|'):
                if '---' in line:
                    separator_line = len(table_data)
                    continue
                # 解析表格行
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                table_data.append(cells)
        
        return table_data, separator_line, markdown_headers
    
    @log_and_reraise(logger, "extract title from row")
    def extract_title_from_row(self, row: List[str]) -> Optional[str]:
        """
        从行中提取标题（符合"1、xxx"、"1. xxx"、"xxx表"格式）
        """
        set_operation_context("extract_title_from_row")
        
        with error_boundary("title extraction", LayerType.UTILITY):
            if not row:
                return None
                
            # 合并非空单元格
            non_empty_cells = [cell for cell in row if cell and cell.strip()]
            if not non_empty_cells:
                return None
                
            combined = ''.join(non_empty_cells)
            
            # 检查是否符合标题格式
            if (re.match(r'^\d+[、.]', combined) or
                combined.endswith('表') or
                re.search(r'表$', combined)):
                logger.debug(f"Extracted title from row: '{combined}'")
                return combined
            
            return None

    @log_and_reraise(logger, "extract table name from header lines")
    def extract_table_name_from_header_lines(self, header_rows: List[List[str]]) -> Optional[str]:
        """
        Priority 1: Extract table name from table header lines (rows 1-5 before separator)
        """
        set_operation_context("extract_table_name_from_header_lines")
        
        with error_boundary("table name extraction from headers", LayerType.UTILITY):
            if not header_rows:
                logger.debug("No header rows provided for table name extraction")
                return None
                
            # Scan first 5 rows for potential table names
            for i, row in enumerate(header_rows[:5]):
                if not row:
                    continue

                # Get non-empty cells
                non_empty_cells = [cell.strip() for cell in row if cell and cell.strip()]

                if not non_empty_cells:
                    continue

                # Criteria 1: Exactly one non-empty cell with length > 3
                if len(non_empty_cells) == 1 and len(non_empty_cells[0]) > 3:
                    cell_content = non_empty_cells[0]
                    # Additional validation: should look like a table name
                    if (cell_content.endswith('表') or
                        re.search(r'.*表$', cell_content) or
                        re.search(r'.*报表$', cell_content) or
                        any(keyword in cell_content for keyword in ['资产负债', '利润', '现金流', '财务', '合并'])):
                        logger.debug(f"Found table name in header row {i+1}: '{cell_content}'")
                        return cell_content

                # Criteria 2 & 3: Any cell ends with '表' or matches table patterns
                for cell in non_empty_cells:
                    if (cell.endswith('表') or
                        re.search(r'.*表$', cell) or
                        re.search(r'.*报表$', cell)):
                        logger.debug(f"Found table name in header row {i+1}: '{cell}'")
                        return cell

            logger.debug("No table name found in header lines")
            return None

    @log_and_reraise(logger, "extract table name from markdown headers")
    def extract_table_name_from_markdown_headers(self, markdown_headers: List[str]) -> Optional[str]:
        """
        Priority 2: Extract table name from markdown headers in first 10 lines
        """
        set_operation_context("extract_table_name_from_markdown_headers")
        
        with error_boundary("table name extraction from markdown headers", LayerType.UTILITY):
            if not markdown_headers:
                logger.debug("No markdown headers provided for table name extraction")
                return None
                
            for header in markdown_headers:
                # Validate header format
                if not isinstance(header, str):
                    logger.warning(f"Invalid header type: {type(header)}, skipping")
                    continue
                    
                # Remove markdown syntax and clean
                clean_header = re.sub(r'^#+\s*', '', header).strip()

                if not clean_header or len(clean_header) <= 3:
                    continue

                # Skip pure metadata headers
                metadata_indicators = ['公司:', '单位:', '编制:', '时间:', '日期:', '币别:']
                if any(indicator in clean_header for indicator in metadata_indicators):
                    continue

                # Check if it's a table name
                if (clean_header.endswith('表') or
                    re.search(r'^[^#]*表[^#]*$', clean_header) or
                    any(keyword in clean_header for keyword in ['资产负债', '利润', '现金流', '财务', '合并', '报表'])):
                    logger.debug(f"Found table name in markdown header: '{clean_header}'")
                    return clean_header

            logger.debug("No table name found in markdown headers")
            return None

    @log_and_reraise(logger, "preprocess filename")
    def preprocess_filename(self, filename: str) -> str:
        """
        Preprocess filename by removing extensions, suffixes, and Excel-related components
        """
        set_operation_context("preprocess_filename")
        
        with error_boundary("filename preprocessing", LayerType.UTILITY):
            if not filename:
                logger.debug("Empty filename provided for preprocessing")
                return ""
                
            if not isinstance(filename, str):
                raise ValidationException(
                    message="Filename must be a string",
                    field_name="filename",
                    details=f"Expected string, got {type(filename)}",
                    context={'input_type': type(filename).__name__},
                    suggested_action="Provide a valid string filename"
                )

            logger.debug(f"Preprocessing filename: '{filename}'")
            original_filename = filename

            # Remove file extensions
            base_name = filename
            for ext in ['.md', '.xlsx', '.xls', '.csv']:
                if base_name.endswith(ext):
                    base_name = base_name[:-len(ext)]
                    break

            # Remove processing suffixes (apply multiple times to handle chained suffixes)
            suffixes = ['_adjusted', '_reformed', '_processed', '_output']
            changed = True
            while changed:
                changed = False
                for suffix in suffixes:
                    if base_name.endswith(suffix):
                        base_name = base_name[:-len(suffix)]
                        changed = True

            # Remove Excel cell positions using strict regex
            base_name = self.remove_excel_cell_positions(base_name)

            # Remove sheet references
            base_name = self.remove_sheet_references(base_name)

            # Remove leading/trailing hyphens and underscores
            base_name = base_name.strip('-_')

            logger.debug(f"Preprocessed filename: '{original_filename}' -> '{base_name}'")
            return base_name

    @log_and_reraise(logger, "remove Excel cell positions")
    def remove_excel_cell_positions(self, text: str) -> str:
        """
        Remove Excel cell position patterns using strict regex
        Must be surrounded by underscores to avoid false matches with date indicators
        """
        set_operation_context("remove_excel_cell_positions")
        
        with error_boundary("Excel cell position removal", LayerType.UTILITY):
            if not isinstance(text, str):
                raise ValidationException(
                    message="Text must be a string",
                    field_name="text",
                    details=f"Expected string, got {type(text)}",
                    context={'input_type': type(text).__name__},
                    suggested_action="Provide a valid string input"
                )
                
            if not text:
                logger.debug("Empty text provided for Excel cell position removal")
                return ""
                
            logger.debug(f"Removing Excel cell positions from: '{text[:100]}...'")
            original_text = text

            try:
                # Pattern 1: Standard Excel cells like _A1O23_, _B2C25_
                # Must have at least 2 characters after the letter(s) to avoid catching Q1, Q2, etc.
                pattern1 = r'_[A-Z]{1,3}\d{2,6}[A-Z]{0,3}\d{0,6}_'
                text = re.sub(pattern1, '_', text)

                # Pattern 2: Excel cells with letter+digit+letter+digit pattern like _A1O23_
                pattern2 = r'_[A-Z]{1,3}\d+[A-Z]+\d+_'
                text = re.sub(pattern2, '_', text)

                # Pattern 3: Complex Excel cells like _AA123BB456_
                pattern3 = r'_[A-Z]{2,3}\d{3,6}[A-Z]{2,3}\d{3,6}_'
                text = re.sub(pattern3, '_', text)

                # Pattern 4: Simple Excel cells but only if they're clearly not date indicators
                # This catches _A1_, _B2_, etc. but avoids _Q1_, _Q2_, etc.
                pattern4 = r'_[A-DF-P,R-Z]\d+_'  # Excludes Q to preserve quarters
                text = re.sub(pattern4, '_', text)

                # Clean up multiple underscores
                text = re.sub(r'_+', '_', text)

                # Remove leading/trailing underscores
                text = text.strip('_')

                logger.debug(f"Excel cell positions removed: '{original_text}' -> '{text}'")
                return text
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to process Excel cell position patterns",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'original_text': original_text},
                    suggested_action="Check regex patterns and input text format"
                )

    @log_and_reraise(logger, "remove sheet references")
    def remove_sheet_references(self, text: str) -> str:
        """
        Remove sheet identifiers like Sheet1, Sheet2, etc.
        Be careful not to remove "Sheet" when it's part of meaningful content
        """
        set_operation_context("remove_sheet_references")
        
        with error_boundary("sheet reference removal", LayerType.UTILITY):
            if not isinstance(text, str):
                raise ValidationException(
                    message="Text must be a string",
                    field_name="text",
                    details=f"Expected string, got {type(text)}",
                    context={'input_type': type(text).__name__},
                    suggested_action="Provide a valid string input"
                )
                
            if not text:
                logger.debug("Empty text provided for sheet reference removal")
                return ""
                
            logger.debug(f"Removing sheet references from: '{text[:100]}...'")
            original_text = text

            try:
                # Pattern to match _Sheet followed by optional digits_
                # This ensures we only remove sheet references that are clearly separated
                pattern = r'_Sheet\d*_'

                # Remove all sheet references
                cleaned_text = re.sub(pattern, '_', text, flags=re.IGNORECASE)

                # Also handle cases where Sheet is at the beginning or end
                cleaned_text = re.sub(r'^Sheet\d*_', '', cleaned_text, flags=re.IGNORECASE)
                cleaned_text = re.sub(r'_Sheet\d*$', '', cleaned_text, flags=re.IGNORECASE)

                # Clean up multiple underscores
                cleaned_text = re.sub(r'_+', '_', cleaned_text)

                # Remove leading/trailing underscores
                cleaned_text = cleaned_text.strip('_')

                logger.debug(f"Sheet references removed: '{original_text}' -> '{cleaned_text}'")
                return cleaned_text
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to process sheet reference patterns",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'original_text': original_text},
                    suggested_action="Check regex patterns and input text format"
                )

    @log_and_reraise(logger, "extract Excel cell positions")
    def extract_excel_cell_positions(self, filename: str) -> List[str]:
        """
        Extract Excel cell position patterns using strict regex
        Must be surrounded by underscores to avoid false matches with date indicators
        """
        set_operation_context("extract_excel_cell_positions")
        
        with error_boundary("Excel cell position extraction", LayerType.UTILITY):
            if not isinstance(filename, str):
                raise ValidationException(
                    message="Filename must be a string",
                    field_name="filename",
                    details=f"Expected string, got {type(filename)}",
                    context={'input_type': type(filename).__name__},
                    suggested_action="Provide a valid string filename"
                )
                
            if not filename:
                logger.debug("Empty filename provided for Excel cell position extraction")
                return []
                
            logger.debug(f"Extracting Excel cell positions from: '{filename}'")

            try:
                # Strict pattern: must be surrounded by underscores
                # Examples: _A1O23_, _B2C25_, _AA123BB456_
                # Must have at least 2 digits to avoid catching Q1, Q2, etc.
                pattern = r'_([A-Z]{1,3}\d{2,6}[A-Z]{0,3}\d{0,6})_'
                matches = re.findall(pattern, filename)

                # Also look for patterns like A1O23 (letter + digit + letter + digits)
                excel_pattern = r'_([A-Z]{1,3}\d+[A-Z]+\d+)_'
                excel_matches = re.findall(excel_pattern, filename)

                # Combine and deduplicate
                all_matches = list(set(matches + excel_matches))

                logger.debug(f"Found Excel cell positions in '{filename}': {all_matches}")
                return all_matches
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to extract Excel cell position patterns",
                    details=f"Regex extraction error: {str(e)}",
                    original_exception=e,
                    context={'filename': filename},
                    suggested_action="Check regex patterns and filename format"
                )

    @log_and_reraise(logger, "extract date period info")
    def extract_date_period_info(self, text: str) -> List[str]:
        """
        Extract comprehensive date/period information from text
        Enhanced with all required Chinese and international date formats
        """
        set_operation_context("extract_date_period_info")
        
        with error_boundary("date period extraction", LayerType.UTILITY):
            # Validate input
            if not isinstance(text, str):
                raise ValidationException(
                    message="Text must be a string",
                    field_name="text",
                    details=f"Expected string, got {type(text)}",
                    context={'input_type': type(text).__name__},
                    suggested_action="Provide a valid string input"
                )
                
            if not text:
                logger.debug("Empty text provided for date period extraction")
                return []

            logger.debug(f"Extracting date/period info from text: '{text[:100]}...'")
            original_text = text

            try:
                date_patterns = [
                    # Specific dates (highest priority)
                    r'(\d{4}-\d{1,2}-\d{1,2})',      # 2024-09-30
                    r'(\d{4}\.\d{1,2}\.\d{1,2})',    # 2024.09.30
                    r'(\d{4}/\d{1,2}/\d{1,2})',      # 2024/09/30
                    r'(\d{4}年\d{1,2}月\d{1,2}日)',   # 2024年9月30日 (Chinese full date)

                    # Year-Month formats (high priority)
                    r'(\d{4}年\d{1,2}月)',           # 2024年9月, 2024年09月
                    r'(\d{4}\.\d{1,2})',             # 2024.8, 2024.03, 2024.12
                    r'(\d{4}-\d{1,2})',              # 2024-08, 2024-12
                    r'(\d{4}/\d{1,2})',              # 2024/09, 2024/12

                    # Quarterly formats (medium priority)
                    r'(\d{4}年[一二三四]季度)',        # 2024年三季度, 2024年一季度
                    r'(\d{4}年Q[1-4])',              # 2024年Q3, 2024年Q1
                    r'(Q[1-4])',                     # Q1, Q2, Q3, Q4
                    r'([一二三四]季度)',              # 三季度, 一季度, 二季度, 四季度

                    # Annual formats (lower priority)
                    r'(\d{4}年)',                    # 2024年, 2023年
                    r'(\d{4})',                      # 2024, 2023 (standalone year)

                    # Parenthetical periods
                    r'\(([^)]*(?:年|Q[1-4]|季度|月)[^)]*)\)',

                    # Underscore-delimited periods
                    r'_([^_]*(?:年|Q[1-4]|季度|月)[^_]*)_',

                    # Hyphen-delimited periods
                    r'-([^-]*(?:年|Q[1-4]|季度|月)[^-]*)-'
                ]

                found_dates = []
                for pattern in date_patterns:
                    matches = re.findall(pattern, text)
                    found_dates.extend(matches)

                # Remove duplicates while preserving order
                unique_dates = []
                for date in found_dates:
                    if date not in unique_dates:
                        unique_dates.append(date)

                logger.debug(f"Extracted date/period info from '{original_text}': {unique_dates}")
                return unique_dates
                
            except re.error as e:
                raise DataProcessingException(
                    message="Failed to extract date/period patterns",
                    details=f"Regex processing error: {str(e)}",
                    original_exception=e,
                    context={'original_text': original_text},
                    suggested_action="Check regex patterns and input text format"
                )

    @handle_layer_boundary(LayerType.UTILITY, "extract_table_type_from_filename")
    def extract_table_type_from_filename(self, filename: str) -> Optional[str]:
        """
        Extract table type from filename with unified error handling
        """
        set_operation_context("extract_table_type_from_filename")
        
        with error_boundary("table type extraction", LayerType.UTILITY):
            if not isinstance(filename, str):
                raise ValidationException(
                    message="Filename must be a string",
                    field_name="filename",
                    details=f"Expected string, got {type(filename)}",
                    context={'input_type': type(filename).__name__},
                    suggested_action="Provide a valid string filename"
                )
                
            if not filename:
                logger.debug("Empty filename provided for table type extraction")
                return None
                
            logger.debug(f"Extracting table type from filename: '{filename}'")
            
            table_types = ['资产负债表', '利润表', '现金流量表', '财务报表', '合并报表', '报表']
            
            for table_type in table_types:
                if table_type in filename:
                    logger.debug(f"Found table type: '{table_type}' in filename")
                    return table_type

            logger.debug("No table type found in filename")
            return None

    @handle_layer_boundary(LayerType.UTILITY, "extract_date_from_header_lines")
    def extract_date_from_header_lines(self, header_rows: List[List[str]]) -> Optional[str]:
        """
        Priority 1: Extract date information from table header lines ONLY (metadata section)
        Strictly focuses on header/metadata rows to avoid false positives from data content.
        Handles cases where date components may be split across multiple cells.
        """
        set_operation_context("extract_date_from_header_lines")
        
        with error_boundary("date extraction from header lines", LayerType.UTILITY):
            if not isinstance(header_rows, list):
                raise ValidationException(
                    message="Header rows must be a list",
                    field_name="header_rows",
                    details=f"Expected list, got {type(header_rows)}",
                    context={'input_type': type(header_rows).__name__},
                    suggested_action="Provide a valid list of header rows"
                )
                
            # Only check actual header rows - no data rows to avoid false positives
            if not header_rows:
                logger.debug("No header rows provided for date extraction")
                return None

            logger.debug(f"Extracting date from {len(header_rows)} header rows (metadata only)")

            # Scan header rows for date information (metadata section only)
            for i, row in enumerate(header_rows):
                if not row:
                    continue

                # Method 1: Check for split date components across cells (highest priority - most specific)
                split_date = self.extract_split_date_from_row(row)
                if split_date:
                    logger.debug(f"Found split date in header row {i+1}: '{split_date}' from row {row}")
                    return split_date

                # Method 2: Check individual cells for complete dates
                for cell in row:
                    if cell and cell.strip():
                        cleaned_cell = cell.strip()
                        dates = self.extract_date_period_info(cleaned_cell)
                        if dates:
                            valid_date = self.validate_and_prioritize_dates(dates)
                            if valid_date:
                                logger.debug(f"Found date in header row {i+1}, cell: '{valid_date}' from '{cleaned_cell}'")
                                return valid_date

                # Method 3: Apply preprocessing and join cells for text analysis
                cleaned_row = self.preprocess_row_for_text_analysis(row)
                row_text = ' '.join(cleaned_row)

                # Extract date information from the cleaned text
                dates = self.extract_date_period_info(row_text)

                if dates:
                    # Validate and prioritize the most specific date
                    valid_date = self.validate_and_prioritize_dates(dates)
                    if valid_date:
                        logger.debug(f"Found date in header row {i+1}: '{valid_date}' from text '{row_text}'")
                        return valid_date

            logger.debug("No date found in header lines")
            return None

    @handle_layer_boundary(LayerType.UTILITY, "extract_date_from_markdown_headers")
    def extract_date_from_markdown_headers(self, markdown_headers: List[str]) -> Optional[str]:
        """
        Extract date information from markdown headers (document metadata) with unified error handling
        """
        set_operation_context("extract_date_from_markdown_headers")
        
        with error_boundary("date extraction from markdown headers", LayerType.UTILITY):
            if not isinstance(markdown_headers, list):
                raise ValidationException(
                    message="Markdown headers must be a list",
                    field_name="markdown_headers",
                    details=f"Expected list, got {type(markdown_headers)}",
                    context={'input_type': type(markdown_headers).__name__},
                    suggested_action="Provide a valid list of markdown headers"
                )
                
            if not markdown_headers:
                logger.debug("No markdown headers provided for date extraction")
                return None

            logger.debug(f"Extracting date from {len(markdown_headers)} markdown headers")

            for i, header in enumerate(markdown_headers):
                # Validate header format
                if not isinstance(header, str):
                    logger.warning(f"Invalid header type: {type(header)}, skipping")
                    continue
                    
                # Remove markdown syntax and clean
                clean_header = re.sub(r'^#+\s*', '', header).strip()

                if not clean_header:
                    continue

                # Extract date information from the cleaned header
                dates = self.extract_date_period_info(clean_header)

                if dates:
                    # Validate and prioritize the most specific date
                    valid_date = self.validate_and_prioritize_dates(dates)
                    if valid_date:
                        logger.debug(f"Found date in markdown header {i+1}: '{valid_date}' from '{clean_header}'")
                        return valid_date

            logger.debug("No date found in markdown headers")
            return None

    @log_and_reraise(logger, "extract split date from row")
    def extract_split_date_from_row(self, row: List[str]) -> Optional[str]:
        """
        Extract dates that may be split across multiple cells or within cells with internal spaces
        Examples: |2024|年|9月|30|日| or | 2024 年 | 9 月 | 30 日 | or |2024 年 9 月|
        """
        set_operation_context("extract_split_date_from_row")
        
        with error_boundary("split date extraction", LayerType.UTILITY):
            if not row:
                return None

        # First, check if any single cell contains a complete date with internal spaces
        for cell in row:
            if cell and cell.strip():
                # Remove internal spaces and check for complete date patterns
                cleaned_cell = re.sub(r'\s+', '', cell.strip())
                if re.match(r'\d{4}年\d{1,2}月(\d{1,2}日)?', cleaned_cell):
                    # Found a complete date in a single cell, validate it
                    dates = self.extract_date_period_info(cleaned_cell)
                    if dates:
                        valid_date = self.validate_and_prioritize_dates(dates)
                        if valid_date:
                            logger.debug(f"Found complete date in single cell: '{valid_date}' from '{cell}'")
                            return valid_date

        # If no complete date found in single cells, proceed with split cell analysis
        # Clean and filter non-empty cells, removing internal spaces
        cleaned_cells = []
        for cell in row:
            if cell and cell.strip():
                # Remove internal spaces for pattern matching
                cleaned_cell = re.sub(r'\s+', '', cell.strip())
                cleaned_cells.append(cleaned_cell)

        if len(cleaned_cells) < 2:  # Reduced from 3 to handle year-month cases
            return None

        # Pattern 1: YYYY-MM-DD split across cells
        # Look for patterns like: ['2024', '-', '09', '-', '30'] or ['2024', '09', '30']
        if len(cleaned_cells) >= 3:
            year_pattern = r'^(20[2-3][0-9])$'
            month_pattern = r'^(0?[1-9]|1[0-2])$'
            day_pattern = r'^(0?[1-9]|[12][0-9]|3[01])$'

            # Try to find year, month, day in sequence
            for i in range(len(cleaned_cells) - 2):
                year_match = re.match(year_pattern, cleaned_cells[i])
                if year_match:
                    year = year_match.group(1)

                    # Look for month in next few cells
                    for j in range(i + 1, min(i + 4, len(cleaned_cells))):
                        month_match = re.match(month_pattern, cleaned_cells[j])
                        if month_match:
                            month = month_match.group(1).zfill(2)

                            # Look for day in next few cells
                            for k in range(j + 1, min(j + 4, len(cleaned_cells))):
                                day_match = re.match(day_pattern, cleaned_cells[k])
                                if day_match:
                                    day = day_match.group(1).zfill(2)

                                    # Construct and validate the date
                                    if self.validate_specific_date(year, month, day):
                                        # Check if this looks like it came from Chinese format
                                        # by looking for Chinese characters in nearby cells
                                        has_chinese_chars = any('年' in str(cell) or '月' in str(cell) or '日' in str(cell)
                                                               for cell in cleaned_cells)

                                        if has_chinese_chars:
                                            constructed_date = f"{year}年{int(month)}月{int(day)}日"
                                        else:
                                            constructed_date = f"{year}-{month}-{day}"

                                        logger.debug(f"Constructed split date: {constructed_date}")
                                        return constructed_date

        # Pattern 2: Chinese date format split across cells
        # Look for patterns like: ['2024', '年', '9', '月', '30', '日']
        year_idx = None
        month_idx = None
        day_idx = None

        for i, cell in enumerate(cleaned_cells):
            if re.match(r'^(20[2-3][0-9])$', cell):
                # Check if next cell is '年'
                if i + 1 < len(cleaned_cells) and cleaned_cells[i + 1] == '年':
                    year_idx = i
            elif re.match(r'^(0?[1-9]|1[0-2])$', cell):
                # Check if next cell is '月'
                if i + 1 < len(cleaned_cells) and cleaned_cells[i + 1] == '月':
                    month_idx = i
            elif re.match(r'^(0?[1-9]|[12][0-9]|3[01])$', cell):
                # Check if next cell is '日'
                if i + 1 < len(cleaned_cells) and cleaned_cells[i + 1] == '日':
                    day_idx = i

        # If we found year, month, and day components
        if year_idx is not None and month_idx is not None and day_idx is not None:
            year = cleaned_cells[year_idx]
            month = cleaned_cells[month_idx].zfill(2)
            day = cleaned_cells[day_idx].zfill(2)

            if self.validate_specific_date(year, month, day):
                # Return in Chinese format to preserve original style
                constructed_date = f"{year}年{int(month)}月{int(day)}日"
                logger.debug(f"Constructed Chinese split date: {constructed_date}")
                return constructed_date

        # If we found year and month but no day
        if year_idx is not None and month_idx is not None:
            year = cleaned_cells[year_idx]
            month = cleaned_cells[month_idx]

            if self.validate_date_format(f"{year}年{month}月"):
                constructed_date = f"{year}年{month}月"
                logger.debug(f"Constructed Chinese year-month: {constructed_date}")
                return constructed_date

        return None

    @log_and_reraise(logger, "extract date from filename")
    def extract_date_from_filename(self, filename: str) -> Optional[str]:
        """
        Priority 2: Extract date information from filename (fallback)
        """
        set_operation_context("extract_date_from_filename")
        
        with error_boundary("date extraction from filename", LayerType.UTILITY):
            if not isinstance(filename, str):
                raise ValidationException(
                    message="Filename must be a string",
                    field_name="filename",
                    details=f"Expected string, got {type(filename)}",
                    context={'input_type': type(filename).__name__},
                    suggested_action="Provide a valid string filename"
                )
                
            if not filename:
                logger.debug("Empty filename provided for date extraction")
                return None

            logger.debug(f"Extracting date from filename: '{filename}'")

            # Use preprocessed filename (after Excel cell and sheet reference filtering)
            preprocessed_filename = self.preprocess_filename(filename)

            # Extract date information
            dates = self.extract_date_period_info(preprocessed_filename)

            if dates:
                # Validate and prioritize the most specific date
                valid_date = self.validate_and_prioritize_dates(dates)
                if valid_date:
                    logger.debug(f"Found date in filename: '{valid_date}' from '{preprocessed_filename}'")
                    return valid_date

            logger.debug("No date found in filename")
            return None

    @log_and_reraise(logger, "validate and prioritize dates")
    def validate_and_prioritize_dates(self, dates: List[str]) -> Optional[str]:
        """
        Validate extracted dates and prioritize the most specific format
        Priority: full date (YYYY-MM-DD) > Chinese full date > year-month > quarter > year
        """
        set_operation_context("validate_and_prioritize_dates")
        
        with error_boundary("date validation and prioritization", LayerType.UTILITY):
            if not isinstance(dates, list):
                raise ValidationException(
                    message="Dates must be a list",
                    field_name="dates",
                    details=f"Expected list, got {type(dates)}",
                    context={'input_type': type(dates).__name__},
                    suggested_action="Provide a valid list of date strings"
                )
                
            if not dates:
                logger.debug("Empty dates list provided for validation")
                return None

            valid_dates = []

            for date in dates:
                if not isinstance(date, str):
                    logger.warning(f"Skipping non-string date: {type(date)}")
                    continue
                if self.validate_date_format(date):
                    valid_dates.append(date)

            if not valid_dates:
                logger.debug("No valid dates found after validation")
                return None

            # Prioritize by specificity (highest priority first)

            # 1. Full dates in international format (2024-09-30, 2024.09.30, 2024/09/30)
            for date in valid_dates:
                if re.match(r'\d{4}[-./]\d{1,2}[-./]\d{1,2}', date):
                    logger.debug(f"Prioritizing full international date: {date}")
                    return date

            # 2. Chinese full dates (2024年9月30日)
            for date in valid_dates:
                if re.match(r'\d{4}年\d{1,2}月\d{1,2}日', date):
                    logger.debug(f"Prioritizing Chinese full date: {date}")
                    return date

            # 3. Year-Month formats (2024年9月, 2024.8, 2024-08, 2024/09)
            for date in valid_dates:
                if (re.match(r'\d{4}年\d{1,2}月', date) or
                    re.match(r'\d{4}[.-/]\d{1,2}', date)):
                    logger.debug(f"Prioritizing year-month date: {date}")
                    return date

            # 4. Quarterly formats with year (2024年Q3, 2024年三季度)
            for date in valid_dates:
                if (re.match(r'\d{4}年[一二三四]季度', date) or
                    re.match(r'\d{4}年Q[1-4]', date)):
                    logger.debug(f"Prioritizing year-quarter date: {date}")
                    return date

            # 5. Standalone quarterly formats (Q1, 三季度)
            for date in valid_dates:
                if (re.match(r'Q[1-4]', date) or
                    re.match(r'[一二三四]季度', date)):
                    logger.debug(f"Prioritizing standalone quarter: {date}")
                    return date

            # 6. Chinese annual formats (2024年) - prioritize over international
            for date in valid_dates:
                if re.match(r'\d{4}年$', date):
                    logger.debug(f"Prioritizing Chinese annual date: {date}")
                    return date

            # 7. International annual formats (2024)
            for date in valid_dates:
                if re.match(r'^\d{4}$', date):
                    logger.debug(f"Prioritizing international annual date: {date}")
                    return date

            # Return first valid date if no priority match
            logger.debug(f"No priority match, returning first valid: {valid_dates[0]}")
            return valid_dates[0]

    @log_and_reraise(logger, "validate date format")
    def validate_date_format(self, date: str) -> bool:
        """
        Validate that extracted date is reasonable
        """
        set_operation_context("validate_date_format")
        
        with error_boundary("date format validation", LayerType.UTILITY):
            if not isinstance(date, str):
                logger.debug(f"Invalid type for date: {type(date)}")
                return False
                
            if not date.strip():
                logger.debug("Empty date string provided")
                return False

            # Check year range (2020-2030)
            year_match = re.search(r'(\d{4})', date)
            if year_match:
                year = int(year_match.group(1))
                if not (2020 <= year <= 2030):
                    logger.debug(f"Year {year} out of valid range 2020-2030")
                    return False

            # Check month range (1-12) for year-month formats
            month_match = re.search(r'\d{4}[-./年](\d{1,2})[月]?', date)
            if month_match:
                month = int(month_match.group(1))
                if not (1 <= month <= 12):
                    logger.debug(f"Month {month} out of valid range 1-12")
                    return False

            # Check day range (1-31) for full date formats
            day_match = re.search(r'\d{4}[-./年]\d{1,2}[-./月](\d{1,2})[日]?', date)
            if day_match:
                day = int(day_match.group(1))
                if not (1 <= day <= 31):
                    logger.debug(f"Day {day} out of valid range 1-31")
                    return False

                # For full dates, do additional calendar validation
                year_match = re.search(r'(\d{4})', date)
                month_match = re.search(r'\d{4}[-./年](\d{1,2})', date)
                if year_match and month_match:
                    year = int(year_match.group(1))
                    month = int(month_match.group(1))
                    if not self.validate_specific_date(str(year), str(month), str(day)):
                        logger.debug(f"Invalid calendar date: {year}-{month}-{day}")
                        return False

            # Check quarter range (1-4) for Q formats
            quarter_match = re.search(r'Q([0-9]+)', date)
            if quarter_match:
                quarter = int(quarter_match.group(1))
                if not (1 <= quarter <= 4):
                    logger.debug(f"Quarter {quarter} out of valid range 1-4")
                    return False

            # Check Chinese quarters
            chinese_quarters = ['一', '二', '三', '四']
            for chinese in chinese_quarters:
                if f'{chinese}季度' in date:
                    # Valid Chinese quarter
                    break

            logger.debug(f"Valid date format: {date}")
            return True

    @log_and_reraise(logger, "validate specific date")
    def validate_specific_date(self, year: str, month: str, day: str) -> bool:
        """
        Validate a specific date (YYYY-MM-DD format)
        """
        set_operation_context("validate_specific_date")
        
        with error_boundary("specific date validation", LayerType.UTILITY):
            if not all(isinstance(param, str) for param in [year, month, day]):
                raise ValidationException(
                    message="Year, month, and day must be strings",
                    field_name="date_components",
                    details=f"Expected string parameters, got types: year={type(year)}, month={type(month)}, day={type(day)}",
                    context={'year_type': type(year).__name__, 'month_type': type(month).__name__, 'day_type': type(day).__name__},
                    suggested_action="Provide string representations of year, month, and day"
                )
                
            try:
                year_int = int(year)
                month_int = int(month)
                day_int = int(day)

                # Check year range
                if not (2020 <= year_int <= 2030):
                    logger.debug(f"Year {year_int} out of valid range 2020-2030")
                    return False

                # Check month range
                if not (1 <= month_int <= 12):
                    logger.debug(f"Month {month_int} out of valid range 1-12")
                    return False

                # Check day range
                if not (1 <= day_int <= 31):
                    logger.debug(f"Day {day_int} out of valid range 1-31")
                    return False

                # Check if it's a valid calendar date
                import calendar
                if month_int in [1, 3, 5, 7, 8, 10, 12]:  # 31-day months
                    valid = day_int <= 31
                elif month_int in [4, 6, 9, 11]:  # 30-day months
                    valid = day_int <= 30
                elif month_int == 2:  # February
                    if calendar.isleap(year_int):
                        valid = day_int <= 29
                    else:
                        valid = day_int <= 28
                else:
                    valid = False

                if valid:
                    logger.debug(f"Valid specific date: {year_int}-{month_int:02d}-{day_int:02d}")
                else:
                    logger.debug(f"Invalid calendar date: {year_int}-{month_int:02d}-{day_int:02d}")
                    
                return valid

            except (ValueError, TypeError) as e:
                raise ValidationException(
                    message="Invalid date component format",
                    field_name="date_components",
                    details=f"Failed to parse date components: year='{year}', month='{month}', day='{day}' - {str(e)}",
                    original_exception=e,
                    context={'year': year, 'month': month, 'day': day},
                    suggested_action="Provide valid numeric date components as strings"
                )

    @log_and_reraise(logger, "normalize date to ISO format")
    def normalize_date_to_iso_format(self, date_str: str) -> str:
        """
        Normalize various date formats to standardized 'YYYY-MM-DD' format.

        Handles:
        - Chinese formats: '2024年9月30日' → '2024-09-30'
        - Chinese year-month: '2024年9月' → '2024-09-30' (last day of month)
        - Chinese quarters: '2024年Q3', '2024年三季度' → '2024-09-30' (last day of quarter)
        - Standalone quarters: 'Q3', '三季度' → current/previous year quarter end
        - ISO formats: '2024-09-30' → '2024-09-30' (already correct)
        - Dot formats: '2024.09.30' → '2024-09-30'
        - Year only: '2024年', '2024' → '2024-12-31' (last day of year)
        """
        set_operation_context("normalize_date_to_iso_format")
        
        with error_boundary("date normalization", LayerType.UTILITY):
            if not isinstance(date_str, str):
                raise ValidationException(
                    message="Date string must be a string",
                    field_name="date_str",
                    details=f"Expected string, got {type(date_str)}",
                    context={'date_str_type': type(date_str).__name__},
                    suggested_action="Provide a string representation of the date"
                )
                
            if not date_str:
                logger.debug("Empty date string provided, returning as-is")
                return date_str

            original_date = date_str
            logger.debug(f"Normalizing date: '{original_date}'")

            current_year = datetime.now().year

            # Pattern 1: Chinese full date (2024年9月30日)
            match = re.match(r'^(\d{4})年(\d{1,2})月(\d{1,2})日$', date_str)
            if match:
                year, month, day = match.groups()
                normalized = f"{year}-{int(month):02d}-{int(day):02d}"
                logger.debug(f"Chinese full date: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 2: Chinese year-month (2024年9月)
            match = re.match(r'^(\d{4})年(\d{1,2})月$', date_str)
            if match:
                year, month = match.groups()
                year_int, month_int = int(year), int(month)
                # Get last day of the month
                last_day = calendar.monthrange(year_int, month_int)[1]
                normalized = f"{year}-{month_int:02d}-{last_day:02d}"
                logger.debug(f"Chinese year-month: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 3: Chinese quarters with year (2024年Q3, 2024年三季度)
            match = re.match(r'^(\d{4})年Q([1-4])$', date_str)
            if not match:
                match = re.match(r'^(\d{4})年([一二三四])季度$', date_str)
                if match:
                    year = match.group(1)
                    chinese_quarter = match.group(2)
                    quarter_map = {'一': '1', '二': '2', '三': '3', '四': '4'}
                    quarter = quarter_map[chinese_quarter]
                else:
                    quarter = None
            else:
                year, quarter = match.groups()

            if match and quarter:
                year_int, quarter_int = int(year), int(quarter)
                # Get last day of quarter
                quarter_end_months = {1: 3, 2: 6, 3: 9, 4: 12}
                end_month = quarter_end_months[quarter_int]
                last_day = calendar.monthrange(year_int, end_month)[1]
                normalized = f"{year}-{end_month:02d}-{last_day:02d}"
                logger.debug(f"Chinese quarter with year: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 4: Standalone quarters (Q3, 三季度) - use most recent completed quarter
            match = re.match(r'^Q([1-4])$', date_str)
            if not match:
                match = re.match(r'^([一二三四])季度$', date_str)
                if match:
                    chinese_quarter = match.group(1)
                    quarter_map = {'一': '1', '二': '2', '三': '3', '四': '4'}
                    quarter = quarter_map[chinese_quarter]
                else:
                    quarter = None
            else:
                quarter = match.group(1)

            if match and quarter:
                quarter_int = int(quarter)
                current_date = datetime.now()
                current_quarter = (current_date.month - 1) // 3 + 1
                current_month = current_date.month
                current_day = current_date.day

                quarter_end_months = {1: 3, 2: 6, 3: 9, 4: 12}
                target_end_month = quarter_end_months[quarter_int]

                # Determine if the target quarter has completed in the current year
                if quarter_int < current_quarter:
                    # Target quarter has already completed this year
                    year_int = current_year
                elif quarter_int == current_quarter:
                    # We're in the target quarter - check if it has ended
                    quarter_end_day = calendar.monthrange(current_year, target_end_month)[1]
                    if current_month == target_end_month and current_day >= quarter_end_day:
                        # Quarter has just ended
                        year_int = current_year
                    elif current_month > target_end_month:
                        # Quarter has ended (shouldn't happen due to quarter calculation, but safety check)
                        year_int = current_year
                    else:
                        # Quarter hasn't ended yet, use previous year
                        year_int = current_year - 1
                else:
                    # Target quarter is in the future, use previous year
                    year_int = current_year - 1

                last_day = calendar.monthrange(year_int, target_end_month)[1]
                normalized = f"{year_int}-{target_end_month:02d}-{last_day:02d}"
                logger.debug(f"Standalone quarter: '{original_date}' → '{normalized}' (most recent completed Q{quarter_int})")
                return normalized

            # Pattern 5: ISO format (2024-09-30) - already correct
            if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                logger.debug(f"ISO format (already correct): '{original_date}'")
                return date_str

            # Pattern 5b: ISO year-month format (2024-09)
            match = re.match(r'^(\d{4})-(\d{1,2})$', date_str)
            if match:
                year, month = match.groups()
                year_int, month_int = int(year), int(month)
                # Get last day of the month
                last_day = calendar.monthrange(year_int, month_int)[1]
                normalized = f"{year}-{month_int:02d}-{last_day:02d}"
                logger.debug(f"ISO year-month format: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 6: Dot format (2024.09.30)
            match = re.match(r'^(\d{4})\.(\d{1,2})\.(\d{1,2})$', date_str)
            if match:
                year, month, day = match.groups()
                normalized = f"{year}-{int(month):02d}-{int(day):02d}"
                logger.debug(f"Dot format: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 6b: Dot year-month format (2024.09)
            match = re.match(r'^(\d{4})\.(\d{1,2})$', date_str)
            if match:
                year, month = match.groups()
                year_int, month_int = int(year), int(month)
                # Get last day of the month
                last_day = calendar.monthrange(year_int, month_int)[1]
                normalized = f"{year}-{month_int:02d}-{last_day:02d}"
                logger.debug(f"Dot year-month format: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 7: Slash format (2024/09/30)
            match = re.match(r'^(\d{4})/(\d{1,2})/(\d{1,2})$', date_str)
            if match:
                year, month, day = match.groups()
                normalized = f"{year}-{int(month):02d}-{int(day):02d}"
                logger.debug(f"Slash format: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 7b: Slash year-month format (2024/09)
            match = re.match(r'^(\d{4})/(\d{1,2})$', date_str)
            if match:
                year, month = match.groups()
                year_int, month_int = int(year), int(month)
                # Get last day of the month
                last_day = calendar.monthrange(year_int, month_int)[1]
                normalized = f"{year}-{month_int:02d}-{last_day:02d}"
                logger.debug(f"Slash year-month format: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 8: Chinese year only (2024年)
            match = re.match(r'^(\d{4})年$', date_str)
            if match:
                year = match.group(1)
                normalized = f"{year}-12-31"
                logger.debug(f"Chinese year only: '{original_date}' → '{normalized}'")
                return normalized

            # Pattern 9: Year only (2024)
            match = re.match(r'^(\d{4})$', date_str)
            if match:
                year = match.group(1)
                normalized = f"{year}-12-31"
                logger.debug(f"Year only: '{original_date}' → '{normalized}'")
                return normalized

            # If no pattern matches, return original
            logger.debug(f"No normalization pattern matched for: '{original_date}'")
            return date_str

    @log_and_reraise(logger, "preprocess row for text analysis")
    def preprocess_row_for_text_analysis(self, row: List[str]) -> List[str]:
        """
        Preprocess row cells for text analysis by cleaning whitespace
        Enhanced to properly handle date components with internal spaces
        """
        set_operation_context("preprocess_row_for_text_analysis")
        
        with error_boundary("row preprocessing", LayerType.UTILITY):
            if not row:
                return []

            cleaned_row = []
            for cell in row:
                if cell is None:
                    cleaned_row.append('')
                else:
                    try:
                        # Strip leading/trailing whitespace
                        cleaned_cell = cell.strip()
                        # For date extraction, remove internal spaces to handle cases like "2024 年 9 月"
                        # This converts "2024 年 9 月" to "2024年9月" for proper pattern matching
                        cleaned_cell = re.sub(r'\s+', '', cleaned_cell)
                        cleaned_row.append(cleaned_cell)
                    except Exception as e:
                        logger.warning(f"Error processing cell '{cell}': {e}")
                        cleaned_row.append(str(cell))

            return cleaned_row

    @handle_layer_boundary(LayerType.UTILITY, "comprehensive date extraction")
    def comprehensive_date_extraction(self, header_rows: List[List[str]], filename: str, markdown_headers: List[str] = None) -> Optional[str]:
        """
        Comprehensive date extraction with strict priority hierarchy for document metadata only:
        1. Table header lines (rows before markdown separator - highest priority)
        2. Markdown headers (document metadata - high priority)
        3. Filename (fallback priority)

        Strictly avoids extracting dates from table data content to prevent false positives where
        data values are mistaken for document dates. Only extracts document/report metadata dates.
        """
        set_operation_context("comprehensive_date_extraction")
        
        with error_boundary("date extraction from metadata", LayerType.UTILITY):
            logger.debug("Starting comprehensive date extraction (metadata only)")

            # Priority 1: Extract from table header lines (metadata only)
            # Only checks rows that appear BEFORE the markdown separator line
            date_from_headers = self.extract_date_from_header_lines(header_rows)
            if date_from_headers:
                logger.debug(f"Priority 1 - Found date in table header metadata: '{date_from_headers}'")
                return date_from_headers

            # Priority 2: Extract from markdown headers (document metadata)
            if markdown_headers:
                date_from_markdown = self.extract_date_from_markdown_headers(markdown_headers)
                if date_from_markdown:
                    logger.debug(f"Priority 2 - Found date in markdown headers: '{date_from_markdown}'")
                    return date_from_markdown

            # Priority 3: Extract from filename (fallback only)
            date_from_filename = self.extract_date_from_filename(filename)
            if date_from_filename:
                logger.debug(f"Priority 3 - Found date in filename: '{date_from_filename}'")
                return date_from_filename

            logger.debug("No document metadata date found - maintaining data integrity by not extracting from table content")
            return None

    @handle_layer_boundary(LayerType.UTILITY, "extract table name from filename")
    def extract_table_name_from_filename(self, filename: str) -> Optional[str]:
        """
        Priority 3: Extract comprehensive table name from filename with strict filtering
        """
        set_operation_context("extract_table_name_from_filename")
        
        with error_boundary("table name extraction from filename", LayerType.UTILITY):
            if not filename:
                return None

            logger.debug(f"Extracting table name from filename: '{filename}'")

            # Preprocess filename (removes extensions, suffixes, Excel cells, sheet refs)
            base_name = self.preprocess_filename(filename)
            logger.debug(f"After preprocessing: '{base_name}'")

            if not base_name:
                return None

            # Apply additional filtering for any remaining unwanted components
            cleaned_name = self.apply_comprehensive_filtering(base_name)
            logger.debug(f"After comprehensive filtering: '{cleaned_name}'")

            if not cleaned_name or len(cleaned_name) <= 3:
                return None

            # Extract date/period information
            date_info = self.extract_date_period_info(cleaned_name)
            logger.debug(f"Extracted date info: {date_info}")

            # Extract table type
            table_type = self.extract_table_type_from_filename(cleaned_name)
            logger.debug(f"Extracted table type: '{table_type}'")

            # Build comprehensive table name
            if table_type and date_info:
                # Combine table type with most specific date info
                most_specific_date = max(date_info, key=len) if date_info else ""
                if most_specific_date:
                    # Check if date info is already in the filename structure
                    if most_specific_date in cleaned_name:
                        logger.debug(f"Using complete cleaned name: '{cleaned_name}'")
                        return cleaned_name
                    else:
                        combined_name = f"{most_specific_date}_{table_type}"
                        logger.debug(f"Using combined name: '{combined_name}'")
                        return combined_name

            # Return cleaned filename if it contains meaningful information
            if len(cleaned_name) > 3:
                logger.debug(f"Using cleaned filename: '{cleaned_name}'")
                return cleaned_name

            return None

    @log_and_reraise(logger, "apply comprehensive filtering")
    def apply_comprehensive_filtering(self, text: str) -> str:
        """
        Apply comprehensive filtering to remove any remaining unwanted components
        """
        set_operation_context("apply_comprehensive_filtering")
        
        with error_boundary("text filtering", LayerType.UTILITY):
            if not text:
                return ""

            original_text = text

            try:
                # Remove any remaining Excel-like patterns that might have been missed
                # Be more specific to avoid removing valid content like Q1, Q2
                # Only remove patterns that clearly look like Excel cells

                # Remove complex Excel patterns
                text = re.sub(r'\b[A-Z]{1,3}\d+[A-Z]+\d+\b', '', text)
                text = re.sub(r'\b[A-Z]{2,3}\d{3,6}[A-Z]{2,3}\d{3,6}\b', '', text)

                # Remove simple Excel cells but preserve date indicators
                text = re.sub(r'\b[A-DF-P,R-Z]\d+\b', '', text)  # Excludes Q to preserve quarters

                # Remove any remaining sheet references (case insensitive)
                text = re.sub(r'\bSheet\d*\b', '', text, flags=re.IGNORECASE)

                # Remove standalone Excel-like patterns that might be separated by underscores
                parts = text.split('_')
                filtered_parts = []
                for part in parts:
                    try:
                        # Skip parts that look like Excel cells
                        if (re.match(r'^[A-Z]{1,3}\d+[A-Z]*\d*$', part) and
                            len(part) > 2 and
                            not part.startswith('Q') and  # Preserve quarters
                            not re.match(r'^\d{4}$', part)):  # Preserve years
                            continue
                        filtered_parts.append(part)
                    except Exception as e:
                        logger.warning(f"Error filtering part '{part}': {e}")
                        filtered_parts.append(part)

                text = '_'.join(filtered_parts)

                # Remove common Excel/processing artifacts
                artifacts = ['_copy', '_Copy', '_COPY', '_backup', '_Backup', '_BACKUP',
                            '_temp', '_Temp', '_TEMP', '_draft', '_Draft', '_DRAFT',
                            '_adjusted', '_reformed', '_processed', '_output']

                for artifact in artifacts:
                    text = text.replace(artifact, '')

                # Remove any remaining processing suffixes that might be embedded
                suffixes = ['adjusted', 'reformed', 'processed', 'output']
                for suffix in suffixes:
                    try:
                        # Remove suffix when it's clearly separated
                        text = re.sub(f'_{suffix}_', '_', text, flags=re.IGNORECASE)
                        text = re.sub(f'_{suffix}$', '', text, flags=re.IGNORECASE)
                        text = re.sub(f'^{suffix}_', '', text, flags=re.IGNORECASE)
                    except Exception as e:
                        logger.warning(f"Error removing suffix '{suffix}': {e}")

                # Clean up separators and whitespace
                text = re.sub(r'_+', '_', text)  # Replace multiple underscores with single underscore
                text = text.strip('_-')  # Remove leading/trailing separators

                # Preserve meaningful content by ensuring we don't remove table types or dates
                # This is a safety check to make sure we haven't over-filtered
                table_indicators = ['资产负债表', '利润表', '现金流量表', '财务报表', '合并报表', '报表']
                date_indicators = ['2024', '2023', '2022', 'Q1', 'Q2', 'Q3', 'Q4', '季度', '年度', '月']

                # If we've removed everything important, try to recover from original
                if not any(indicator in text for indicator in table_indicators + date_indicators):
                    logger.warning(f"Comprehensive filtering may have over-filtered: '{text}' from '{original_text}'")
                    # Return original text as fallback
                    return original_text

                logger.debug(f"Comprehensive filtering: '{original_text}' -> '{text}'")
                return text
                
            except Exception as e:
                logger.warning(f"Error in comprehensive filtering: {e}")
                return original_text

    @handle_layer_boundary(LayerType.UTILITY, "hierarchical table name extraction")
    def hierarchical_table_name_extraction(self, header_rows: List[List[str]],
                                         markdown_headers: List[str],
                                         filename: str) -> str:
        """
        Hierarchical table name extraction with priority order:
        1. Table header lines (highest priority)
        2. Markdown headers (medium priority)
        3. Filename (fallback priority)
        """
        set_operation_context("hierarchical_table_name_extraction")
        
        with error_boundary("hierarchical table name extraction", LayerType.UTILITY):
            logger.debug("Starting hierarchical table name extraction")

            # Validate inputs
            if not isinstance(header_rows, list):
                raise ValidationException(
                    message="header_rows must be a list",
                    field_name="header_rows",
                    details=f"Expected list, got {type(header_rows)}",
                    context={'input_type': type(header_rows).__name__},
                    suggested_action="Provide a list of header rows"
                )

            if not isinstance(markdown_headers, list):
                raise ValidationException(
                    message="markdown_headers must be a list",
                    field_name="markdown_headers",
                    details=f"Expected list, got {type(markdown_headers)}",
                    context={'input_type': type(markdown_headers).__name__},
                    suggested_action="Provide a list of markdown headers"
                )

            if not isinstance(filename, str):
                raise ValidationException(
                    message="filename must be a string",
                    field_name="filename",
                    details=f"Expected string, got {type(filename)}",
                    context={'input_type': type(filename).__name__},
                    suggested_action="Provide a string filename"
                )

            # Priority 1: Extract from table header lines
            table_name = self.extract_table_name_from_header_lines(header_rows)
            if table_name:
                logger.debug(f"Priority 1 - Found table name in header lines: '{table_name}'")

                # For Chinese filenames, check if filename has more comprehensive info
                if filename and re.search(r'[\u4e00-\u9fff]', filename):
                    filename_name = self.extract_table_name_from_filename(filename)
                    if filename_name:
                        # Check prioritization criteria
                        filename_length = len(filename_name)
                        table_name_length = len(table_name)
                        has_date_info = any(pattern in filename_name for pattern in ['2024', 'Q1', 'Q2', 'Q3', 'Q4', '季度'])

                        if (filename_length > table_name_length or
                            has_date_info or
                            any(keyword in filename_name for keyword in ['合并', '季度', '年度'])):
                            logger.debug(f"Filename has more comprehensive info, using: '{filename_name}'")
                            return filename_name

                return table_name

            # Priority 2: Extract from markdown headers
            table_name = self.extract_table_name_from_markdown_headers(markdown_headers)
            if table_name:
                logger.debug(f"Priority 2 - Found table name in markdown headers: '{table_name}'")

                # For Chinese filenames, check if filename has more comprehensive info
                if filename and re.search(r'[\u4e00-\u9fff]', filename):
                    filename_name = self.extract_table_name_from_filename(filename)
                    if filename_name:
                        # Check prioritization criteria
                        filename_length = len(filename_name)
                        table_name_length = len(table_name)
                        has_date_info = any(pattern in filename_name for pattern in ['2024', 'Q1', 'Q2', 'Q3', 'Q4', '季度'])

                        if (filename_length > table_name_length or
                            has_date_info or
                            any(keyword in filename_name for keyword in ['合并', '季度', '年度'])):
                            logger.debug(f"Filename has more comprehensive info, using: '{filename_name}'")
                            return filename_name

                return table_name

            # Priority 3: Extract from filename (fallback)
            table_name = self.extract_table_name_from_filename(filename)
            if table_name:
                logger.debug(f"Priority 3 - Found table name in filename: '{table_name}'")
                return table_name

            # Final fallback: use preprocessed filename
            fallback_name = self.preprocess_filename(filename) if filename else "未知表格"
            logger.debug(f"Using fallback table name: '{fallback_name}'")
            return fallback_name

    @log_and_reraise(logger, "check date or metadata keywords")
    def contains_date_or_metadata_keywords(self, row: List[str]) -> bool:
        """
        检查行是否包含日期或元数据关键词
        """
        set_operation_context("contains_date_or_metadata_keywords")
        
        with error_boundary(LayerType.UTILITY, "检查日期或元数据关键词"):
            if not row:
                return False
                
            row_text = ' '.join(row)
            
            # 检查日期格式
            if re.search(r'\d{4}\s*年|\d{1,2}\s*月|\d{1,2}\s*日', row_text):
                return True
                
            # 检查元数据关键词
            for keyword in self.metadata_keywords:
                if keyword in row_text:
                    return True
                    
            return False
    
    @log_and_reraise(logger, "check header keywords")
    def contains_header_keywords(self, row: List[str]) -> bool:
        """
        检查行是否包含表头关键词
        """
        set_operation_context("contains_header_keywords")
        
        with error_boundary(LayerType.UTILITY, "检查表头关键词"):
            if not row:
                return False
                
            row_text = ' '.join(row)
            for keyword in self.header_keywords:
                if keyword in row_text:
                    return True
            return False

    @log_and_reraise(logger, "analyze row density")
    def analyze_row_density(self, row: List[str]) -> float:
        """
        分析行密度（非空单元格比例）
        """
        set_operation_context("analyze_row_density")
        
        with error_boundary(LayerType.UTILITY, "分析行密度"):
            if not row:
                return 0.0
            non_empty_count = sum(1 for cell in row if cell.strip())
            return non_empty_count / len(row)

    @log_and_reraise(logger, "check colon separators")
    def has_colon_separators(self, row: List[str]) -> bool:
        """
        检查行是否包含冒号分隔符（表示键值对）
        """
        set_operation_context("has_colon_separators")
        
        with error_boundary(LayerType.UTILITY, "检查冒号分隔符"):
            if not row:
                return False
                
            row_text = ' '.join(row)
            return ':' in row_text or '：' in row_text

    @log_and_reraise(logger, "check date patterns")
    def has_date_patterns(self, row: List[str]) -> bool:
        """
        检查行是否包含日期模式
        """
        set_operation_context("has_date_patterns")
        
        with error_boundary(LayerType.UTILITY, "检查日期模式"):
            if not row:
                return False
                
            row_text = ' '.join(row)
            date_patterns = [
                r'\d{4}\s*年',
                r'\d{1,2}\s*月',
                r'\d{1,2}\s*日',
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{4}/\d{1,2}/\d{1,2}'
            ]
            return any(re.search(pattern, row_text) for pattern in date_patterns)

    @log_and_reraise(logger, "analyze time keyword context")
    def analyze_time_keyword_context(self, row: List[str], row_index: int, total_rows: int) -> str:
        """
        分析"时间"关键词的上下文，返回分类建议：'header', 'metadata', 'ambiguous'
        """
        set_operation_context("analyze_time_keyword_context")
        
        with error_boundary(LayerType.UTILITY, "分析时间关键词上下文"):
            if not row:
                return 'ambiguous'
                
            row_text = ' '.join(row)

            # 如果不包含"时间"，返回模糊
            if '时间' not in row_text:
                return 'ambiguous'

            # 计算行密度
            density = self.analyze_row_density(row)

            # 检查是否有冒号分隔符
            has_colons = self.has_colon_separators(row)

            # 检查是否有日期模式
            has_dates = self.has_date_patterns(row)

            # 检查"时间"右侧是否有日期值
            time_with_date = False
            for i, cell in enumerate(row):
                if '时间' in cell and i + 1 < len(row):
                    next_cell = row[i + 1]
                    if self.has_date_patterns([next_cell]):
                        time_with_date = True
                        break

            # 评分系统
            header_score = 0
            metadata_score = 0

        # 位置权重：早期行更可能是元数据
        position_ratio = row_index / max(total_rows, 1)
        if position_ratio < 0.3:  # 前30%的行
            metadata_score += 2
        elif position_ratio > 0.7:  # 后30%的行
            header_score += 2

        # 密度分析：低密度更可能是元数据
        if density < 0.5:
            metadata_score += 2
        elif density > 0.8:
            header_score += 2

        # 冒号分隔符：强烈表示元数据
        if has_colons:
            metadata_score += 3

        # 时间后跟日期值：强烈表示元数据
        if time_with_date:
            metadata_score += 3

        # 日期模式：倾向于元数据
        if has_dates:
            metadata_score += 1

        # 结构化列头上下文：检查是否在表头行中
        if density > 0.6 and not has_colons and not time_with_date:
            header_score += 2

        # 返回分类结果
        if metadata_score > header_score + 1:
            return 'metadata'
        elif header_score > metadata_score + 1:
            return 'header'
        else:
            return 'ambiguous'

    @log_and_reraise(logger, "calculate classification score")
    def calculate_classification_score(self, row: List[str], row_index: int, total_rows: int) -> dict:
        """
        计算行的分类得分，返回包含header_score和metadata_score的字典
        """
        set_operation_context("calculate_classification_score", {
            "row_index": row_index,
            "total_rows": total_rows,
            "row_length": len(row) if row else 0
        })
        
        with error_boundary(LayerType.UTILITY, "计算分类得分"):
            scores = {'header_score': 0, 'metadata_score': 0}

            if not row:
                return scores

            # 基础关键词检查
            has_header_keywords = self.contains_header_keywords(row)
            has_metadata_keywords = self.contains_date_or_metadata_keywords(row)

            # 位置权重
            position_ratio = row_index / max(total_rows, 1)

            # 行特征分析
            density = self.analyze_row_density(row)
            has_colons = self.has_colon_separators(row)
            has_dates = self.has_date_patterns(row)

            # 基础关键词得分
            if has_header_keywords:
                scores['header_score'] += 3
            if has_metadata_keywords:
                scores['metadata_score'] += 3

            # 特殊处理"时间"关键词
            if '时间' in ' '.join(row):
                time_context = self.analyze_time_keyword_context(row, row_index, total_rows)
                if time_context == 'header':
                    scores['header_score'] += 2
                    scores['metadata_score'] -= 1
                elif time_context == 'metadata':
                    scores['metadata_score'] += 2
                    scores['header_score'] -= 1

            # 位置权重调整
            if position_ratio < 0.3:  # 早期行
                scores['metadata_score'] += 1
            elif position_ratio > 0.7:  # 后期行
                scores['header_score'] += 1

            # 结构特征调整
            if density < 0.4:  # 低密度
                scores['metadata_score'] += 2
            elif density > 0.8:  # 高密度
                scores['header_score'] += 1

            if has_colons:  # 冒号分隔符
                scores['metadata_score'] += 3

            if has_dates:  # 日期模式
                scores['metadata_score'] += 1

            return scores
    
    @log_and_reraise(logger, "count non-empty content")
    def count_non_empty_content(self, rows: List[List[str]]) -> int:
        """
        统计多行垂直合并后的非空内容数量
        """
        set_operation_context("count_non_empty_content")
        
        with error_boundary(LayerType.UTILITY, "统计非空内容数量"):
            if not rows:
                return 0
                
            # 按列合并内容
            col_count = max(len(row) for row in rows) if rows else 0
            non_empty_cols = 0
            
            for col_idx in range(col_count):
                col_content = []
                for row in rows:
                    if col_idx < len(row) and row[col_idx].strip():
                        col_content.append(row[col_idx].strip())
                
                if col_content:
                    non_empty_cols += 1
                    
            return non_empty_cols
    
    @log_and_reraise(logger, "format header row")
    def format_header_row(self, rows: List[List[str]]) -> str:
        """
        将多行合并为标题格式："内容1 - 【内容2】"
        """
        set_operation_context("format_header_row")
        
        with error_boundary(LayerType.UTILITY, "格式化表头行"):
            if not rows:
                return ""
                
            # 收集所有非空内容
            all_content = []
            for row in rows:
                for cell in row:
                    if cell.strip():
                        all_content.append(cell.strip())
            
            if len(all_content) == 0:
                return ""
            elif len(all_content) == 1:
                return all_content[0]
            elif len(all_content) == 2:
                return f"{all_content[0]} - 【{all_content[1]}】"
            else:
                # 多于2个内容时，取前两个
                return f"{all_content[0]} - 【{all_content[1]}】"
    
    @log_and_reraise(logger, "extract metadata from row")
    def extract_metadata_from_row(self, row: List[str]) -> List[str]:
        """
        从行中提取元数据，用空cell分割
        """
        set_operation_context("extract_metadata_from_row")
        
        with error_boundary(LayerType.UTILITY, "从行中提取元数据"):
            if not row:
                return []
                
            metadata = []
            
            for cell in row:
                if cell.strip():
                    # 直接添加每个非空单元格作为单独的元数据项
                    metadata.append(cell.strip())
            
            return metadata
    
    @log_and_reraise(logger, "extract subtitle")
    def extract_subtitle(self, metadata_list: List[str]) -> Optional[str]:
        """
        从元数据中提取副标题（包含"表"的内容）
        """
        set_operation_context("extract_subtitle")
        
        with error_boundary(LayerType.UTILITY, "提取副标题"):
            if not metadata_list:
                return None
                
            for metadata in metadata_list:
                if '表' in metadata and not metadata.startswith('编制'):
                    return metadata
            return None
    
    @log_and_reraise(logger, "merge header rows")
    def merge_header_rows(self, rows: List[List[str]]) -> List[str]:
        """
        多级表头合并：按列由上而下join，最多有两个非空内容
        """
        set_operation_context("merge_header_rows")
        
        with error_boundary(LayerType.UTILITY, "合并表头行"):
            if not rows:
                return []
            
            # 获取最大列数
            max_cols = max(len(row) for row in rows) if rows else 0
            merged_header = []
            
            for col_idx in range(max_cols):
                # 收集该列的所有非空内容
                col_content = []
                for row in rows:
                    if col_idx < len(row) and row[col_idx].strip():
                        col_content.append(row[col_idx].strip())
                
                # 合并该列内容
                if not col_content:
                    merged_header.append("")
                elif len(col_content) == 1:
                    merged_header.append(col_content[0])
                elif len(col_content) == 2:
                    merged_header.append(f"{col_content[0]} - 【{col_content[1]}】")
                else:
                    # 超过2个内容时，取前两个
                    merged_header.append(f"{col_content[0]} - 【{col_content[1]}】")
            
            # 过滤掉末尾的空列
            while merged_header and not merged_header[-1]:
                merged_header.pop()
            
            return merged_header
    
    @log_and_reraise(logger, "check can merge as header")
    def can_merge_as_header(self, rows: List[List[str]]) -> bool:
        """
        判断多行是否可以合并为表头（每列最多2个非空内容）
        """
        set_operation_context("can_merge_as_header")
        
        with error_boundary(LayerType.UTILITY, "判断是否可以合并为表头"):
            if not rows:
                return False
            
            max_cols = max(len(row) for row in rows) if rows else 0
            
            for col_idx in range(max_cols):
                col_content = []
                for row in rows:
                    if col_idx < len(row) and row[col_idx].strip():
                        col_content.append(row[col_idx].strip())
                
                if len(col_content) > 2:
                    return False
            
            return True
    
    @log_and_reraise(logger, "extract metadata from markdown headers")
    def extract_metadata_from_markdown_headers(self, headers: List[str]) -> Tuple[str, List[str]]:
        """
        从markdown标题中提取表名和元数据
        """
        set_operation_context("extract_metadata_from_markdown_headers")
        
        with error_boundary(LayerType.UTILITY, "从markdown标题中提取元数据"):
            if not headers:
                return "", []
                
            main_title = ""
            metadata_list = []
            
            # Define header keywords to exclude
            header_keywords = ['时间', '机构', '编制', '单位', '公司', '日期', '项目',
                              '科目', '月度', '期间', '行次', '序号']
            
            for header in headers:
                # 移除markdown标记
                clean_header = re.sub(r'^#+\s*', '', header).strip()
                
                # 检查是否为表名
                if clean_header.endswith('表') or re.search(r'表$', clean_header):
                    if not main_title:
                        main_title = clean_header
                else:
                    # Only add to metadata if it's not a header keyword
                    if not any(keyword in clean_header for keyword in header_keywords):
                        metadata_list.append(clean_header)
            
            return main_title, metadata_list
    
    @handle_layer_boundary(LayerType.UTILITY, "process_table")
    def process_table(self, content: str, filename: str = "") -> str:
        """
        处理markdown表格，提取元数据并重整结构
        """
        set_operation_context("process_table")
        
        with error_boundary(LayerType.UTILITY, "处理markdown表格"):
            logger.debug(f"Processing table for file: {filename}")
            table_data, separator_line, markdown_headers = self.parse_markdown_table(content)

            logger.debug(f"Found {len(table_data)} rows, separator at {separator_line}")

            if separator_line == -1:
                return content  # 没有找到分割行，返回原内容
            
            # 分离表头和表体
            header_rows = table_data[:separator_line]
            body_rows = table_data[separator_line:]

            # 使用分层表名提取系统
            main_title = self.hierarchical_table_name_extraction(header_rows, markdown_headers, filename)

            # 使用综合日期提取系统 (strict metadata-only extraction)
            extracted_date = self.comprehensive_date_extraction(header_rows, filename, markdown_headers)

            # 从markdown标题中提取元数据（不包括表名）
            _, metadata_from_headers = self.extract_metadata_from_markdown_headers(markdown_headers)
                
        # 过滤掉只包含SheetX的元数据
        filtered_metadata = []
        for metadata in metadata_from_headers:
            if not re.match(r'^Sheet\d*$', metadata.strip()):
                filtered_metadata.append(metadata)
        metadata_from_headers = filtered_metadata

        # 如果表头行数少于等于1，说明表格结构已经完整
        if len(header_rows) <= 1:
            # 构建输出
            result = []
            
            # 创建元数据字典
            meta_dict = {}
            if filename:
                meta_dict["file"] = filename

            if main_title:
                meta_dict["table_name"] = main_title

            if extracted_date:
                # Normalize date to standard YYYY-MM-DD format before saving to JSON
                normalized_date = self.normalize_date_to_iso_format(extracted_date)
                meta_dict["date"] = normalized_date

            if metadata_from_headers:
                meta_dict["metadata"] = metadata_from_headers
            
            # 添加单行JSON元数据
            result.append(f"<!-- METADATA: {json.dumps(meta_dict, ensure_ascii=False)} -->")
            result.append('')  # 空行分隔
            result.append(f"# {main_title}")
            
            # 重整后的表格（保持原样）
            if header_rows:
                result.append('| ' + ' | '.join(header_rows[0]) + ' |')
                result.append('| ' + ' | '.join(['---'] * len(header_rows[0])) + ' |')
            # 添加表格主体
            for row in body_rows:
                if row:  # 跳过空行
                    result.append('| ' + ' | '.join(row) + ' |')
            
            return '\n'.join(result)
        
        # 处理多行表头的情况
        subtitle = ""
        metadata_list = list(metadata_from_headers)  # 复制从markdown标题中提取的元数据
        remaining_header_rows = []
        
        # 第一步：提取表名和元数据
        i = 0
        while i < len(header_rows):
            row = header_rows[i]

            # 检查是否为标题行（用于副标题提取）
            title = self.extract_title_from_row(row)
            if title:
                # 不覆盖主标题，只设置副标题
                if not subtitle:
                    subtitle = title
                i += 1
                continue

            # 使用智能分类系统
            scores = self.calculate_classification_score(row, i, len(header_rows))

            # 如果明确是表头，则停止处理并保留剩余行
            if scores['header_score'] > scores['metadata_score'] + 2:
                remaining_header_rows.extend(header_rows[i:])
                break

            # 如果明确是元数据，则提取
            if scores['metadata_score'] > scores['header_score'] + 1:
                metadata = self.extract_metadata_from_row(row)
                metadata_list.extend(metadata)
                i += 1
                continue

            # 模糊情况：使用传统逻辑作为后备
            if self.contains_header_keywords(row):
                remaining_header_rows.extend(header_rows[i:])
                break
            elif self.contains_date_or_metadata_keywords(row):
                metadata = self.extract_metadata_from_row(row)
                metadata_list.extend(metadata)
                i += 1
                continue

            # 剩余的行暂存
            remaining_header_rows.extend(header_rows[i:])
            break
        
        # 第二步：处理剩余的表头行
        final_header = []

        # 使用智能分类系统处理剩余行
        processed_indices = []
        for idx, row in enumerate(remaining_header_rows):
            scores = self.calculate_classification_score(row, idx, len(remaining_header_rows))

            # 如果明确是元数据且不是表头，则提取
            if (scores['metadata_score'] > scores['header_score'] + 2 and
                scores['header_score'] < 3):  # 确保不是强表头信号
                metadata = self.extract_metadata_from_row(row)
                metadata_list.extend(metadata)
                processed_indices.append(idx)

        # 移除已处理的元数据行
        remaining_header_rows = [row for idx, row in enumerate(remaining_header_rows)
                               if idx not in processed_indices]
        
        # 处理剩余的表头行（这些应该是真正的表头）
        if remaining_header_rows:
            if self.can_merge_as_header(remaining_header_rows):
                final_header = self.merge_header_rows(remaining_header_rows)
            else:
                # 如果不能合并，则取最后一行作为表头
                final_header = [cell.strip() for cell in remaining_header_rows[-1] if cell.strip()]
        
        # 确保至少有一行表头
        if not final_header and header_rows:
            # 使用最后一个原始表头行
            final_header = [cell.strip() for cell in header_rows[-1] if cell.strip()]
        
        # 从元数据中提取副标题
        extracted_subtitle = self.extract_subtitle(metadata_list)
        if extracted_subtitle and not subtitle:
            subtitle = extracted_subtitle
            metadata_list = [m for m in metadata_list if m != subtitle]
        
        # 创建元数据字典
        meta_dict = {}

        # 添加文件名到元数据
        if filename:
            meta_dict["file"] = filename

        if main_title:
            meta_dict["table_name"] = main_title

        # 使用综合日期提取系统（优先级高于副标题中的时间信息）
        if extracted_date:
            # Normalize date to standard YYYY-MM-DD format before saving to JSON
            normalized_date = self.normalize_date_to_iso_format(extracted_date)
            meta_dict["date"] = normalized_date

        # 标题信息 - 需要分离时间信息
        time_info = ""
        if subtitle and subtitle != main_title:
            # 检查副标题是否包含时间信息（仅在没有从其他地方提取到日期时使用）
            if not extracted_date and re.search(r'\d{4}\s*年|\d{1,2}\s*月', subtitle):
                # 分离时间和其他信息
                time_match = re.search(r'(\d{4}\s*年\s*\d{1,2}\s*月)', subtitle)
                if time_match:
                    time_info = time_match.group(1).replace(' ', '')
                    # 移除时间信息后的剩余部分
                    remaining = re.sub(r'\d{4}\s*年\s*\d{1,2}\s*月\s*', '', subtitle).strip()
                    if remaining:
                        meta_dict["subtitle"] = remaining
                else:
                    meta_dict["subtitle"] = subtitle
            else:
                meta_dict["subtitle"] = subtitle

        # 添加时间信息（仅在没有从综合日期提取中获得日期时）
        if time_info and not extracted_date:
            # Normalize time_info date to standard YYYY-MM-DD format before saving to JSON
            normalized_time_info = self.normalize_date_to_iso_format(time_info)
            meta_dict["date"] = normalized_time_info
        
        # 元数据 - 过滤掉SheetX和表头关键词
        filtered_metadata_list = []
        for metadata in metadata_list:
            # 跳过Sheet标识
            if re.match(r'^Sheet\d*$', metadata.strip()):
                continue

            # 检查是否为单独的表头关键词
            is_header_keyword = False
            metadata_clean = metadata.strip()

            # 如果元数据项就是表头关键词本身，则跳过
            if metadata_clean in self.header_keywords:
                is_header_keyword = True

            # 对于包含"时间"的项，进行上下文分析
            if '时间' in metadata_clean and not is_header_keyword:
                # 创建模拟行进行分析
                mock_row = [metadata_clean]
                time_context = self.analyze_time_keyword_context(mock_row, 0, 1)
                if time_context == 'header':
                    is_header_keyword = True

            if not is_header_keyword:
                filtered_metadata_list.append(metadata)

        if filtered_metadata_list:
            meta_dict["metadata"] = filtered_metadata_list
        
        # 构建输出
        result = []
        
        # 添加单行JSON元数据
        result.append(f"<!-- METADATA: {json.dumps(meta_dict, ensure_ascii=False)} -->")
        result.append('')  # 空行分隔
        
        # 添加标题
        if main_title:
            result.append(f"# {main_title}")
        
        # 重整后的表格
        if final_header:
            # 构建表格头部
            result.append('| ' + ' | '.join(final_header) + ' |')
            result.append('| ' + ' | '.join(['---'] * len(final_header)) + ' |')
        
        # 添加表格主体
        for row in body_rows:
            if row:  # 跳过空行
                result.append('| ' + ' | '.join(row) + ' |')
        
        return '\n'.join(result)
    
    @handle_layer_boundary(LayerType.UTILITY, "extract_and_save_metadata")
    def extract_and_save_metadata(self, content: str, output_file_path: str) -> str:
        """
        Extract metadata from content and save separately if present.
        Returns the content with metadata comment removed.
        """
        set_operation_context("extract_and_save_metadata")
        
        with error_boundary("metadata extraction and saving", LayerType.UTILITY):
            # Validate inputs
            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__},
                    suggested_action="Provide valid string content"
                )
                
            if not isinstance(output_file_path, str):
                raise ValidationException(
                    message="Output file path must be a string",
                    field_name="output_file_path",
                    details=f"Expected string, got {type(output_file_path)}",
                    context={'input_type': type(output_file_path).__name__},
                    suggested_action="Provide a valid file path string"
                )
                
            if not output_file_path:
                raise ValidationException(
                    message="Output file path cannot be empty",
                    field_name="output_file_path",
                    details="Empty file path provided",
                    suggested_action="Provide a non-empty file path"
                )

            lines = content.split('\n')
            if not lines:
                logger.debug("Empty content provided")
                return content

            first_line = lines[0].strip()
            if not first_line.startswith('<!-- METADATA:'):
                logger.debug("No metadata comment found in content")
                return content

            try:
                # Extract JSON content from metadata comment
                # Format: <!-- METADATA: {json_content} -->
                start_marker = '<!-- METADATA:'
                end_marker = '-->'

                if not first_line.endswith(end_marker):
                    raise ValidationException(
                        message="Malformed metadata comment format",
                        details=f"Comment does not end with '{end_marker}': {first_line}",
                        context={'malformed_line': first_line},
                        suggested_action="Check metadata comment format"
                    )

                # Extract JSON string
                json_start = first_line.find(start_marker) + len(start_marker)
                json_end = first_line.rfind(end_marker)
                json_str = first_line[json_start:json_end].strip()

                if not json_str:
                    raise ValidationException(
                        message="Empty JSON content in metadata comment",
                        details="No JSON content found between metadata markers",
                        context={'comment_line': first_line},
                        suggested_action="Check metadata comment contains valid JSON"
                    )

                # Parse JSON
                try:
                    metadata = json.loads(json_str)
                except json.JSONDecodeError as e:
                    raise DataProcessingException(
                        message="Failed to parse metadata JSON",
                        details=f"Invalid JSON format: {str(e)}",
                        original_exception=e,
                        context={'json_content': json_str},
                        suggested_action="Validate JSON syntax in metadata comment"
                    )

                # Generate JSON file path
                base_path = os.path.splitext(output_file_path)[0]
                json_file_path = f"{base_path}.json"

                # Save metadata to JSON file
                try:
                    with open(json_file_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, ensure_ascii=False, indent=2)
                except IOError as e:
                    raise FileProcessingException(
                        message="Failed to save metadata to JSON file",
                        details=f"File I/O error when writing {json_file_path}: {str(e)}",
                        original_exception=e,
                        context={'output_file': json_file_path, 'metadata': metadata},
                        suggested_action="Check file permissions and disk space"
                    )

                logger.info(f"Metadata saved to: {json_file_path}")

                # Remove metadata comment line from content
                remaining_lines = lines[1:]
                # Remove empty line after metadata if present
                if remaining_lines and not remaining_lines[0].strip():
                    remaining_lines = remaining_lines[1:]

                return '\n'.join(remaining_lines)

            except (ValidationException, DataProcessingException, FileProcessingException):
                raise
            except Exception as e:
                raise DataProcessingException(
                    message="Unexpected error processing metadata",
                    details=f"Error during metadata processing: {str(e)}",
                    original_exception=e,
                    context={'output_file_path': output_file_path, 'content_length': len(content)},
                    suggested_action="Check file content and system resources"
                )

    @handle_layer_boundary(LayerType.UTILITY, "process_file")
    def process_file(self, input_file: str, output_file: str = None):
        """
        处理单个markdown文件
        """
        set_operation_context("process_file")
        
        with error_boundary("single file processing", LayerType.UTILITY):
            # Validate inputs
            if not isinstance(input_file, str):
                raise ValidationException(
                    message="Input file must be a string",
                    field_name="input_file",
                    details=f"Expected string, got {type(input_file)}",
                    context={'input_type': type(input_file).__name__},
                    suggested_action="Provide a valid file path string"
                )
                
            if not input_file:
                raise ValidationException(
                    message="Input file path cannot be empty",
                    field_name="input_file",
                    details="Empty file path provided",
                    suggested_action="Provide a non-empty file path"
                )
                
            if output_file is not None and not isinstance(output_file, str):
                raise ValidationException(
                    message="Output file must be a string or None",
                    field_name="output_file",
                    details=f"Expected string or None, got {type(output_file)}",
                    context={'input_type': type(output_file).__name__},
                    suggested_action="Provide a valid output file path or None"
                )

            logger.debug(f"Processing file: {input_file}")
            
            # Check file existence
            if not os.path.exists(input_file):
                raise FileProcessingException(
                    message="Input file does not exist",
                    details=f"File not found: {input_file}",
                    context={'file_path': input_file},
                    suggested_action="Verify file path and ensure file exists"
                )
                
            if not os.path.isfile(input_file):
                raise ValidationException(
                    message="Input path is not a file",
                    details=f"Path exists but is not a file: {input_file}",
                    context={'file_path': input_file},
                    suggested_action="Provide a valid file path"
                )

            # Read file content
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    content = f.read()
            except IOError as e:
                raise FileProcessingException(
                    message="Failed to read input file",
                    details=f"File I/O error when reading {input_file}: {str(e)}",
                    original_exception=e,
                    context={'file_path': input_file},
                    suggested_action="Check file permissions and encoding"
                )

            filename = os.path.basename(input_file)
            
            try:
                result = self.process_table(content, filename)
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to process table content",
                    details=f"Error processing table from {input_file}: {str(e)}",
                    original_exception=e,
                    context={'file_path': input_file, 'content_length': len(content)},
                    suggested_action="Check file content format and structure"
                )

            if output_file:
                # Extract and save metadata separately
                try:
                    clean_content = self.extract_and_save_metadata(result, output_file)
                except Exception as e:
                    raise DataProcessingException(
                        message="Failed to extract and save metadata",
                        details=f"Error extracting metadata for {output_file}: {str(e)}",
                        original_exception=e,
                        context={'output_file': output_file},
                        suggested_action="Check output file path and permissions"
                    )

                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(clean_content)
                except IOError as e:
                    raise FileProcessingException(
                        message="Failed to write output file",
                        details=f"File I/O error when writing {output_file}: {str(e)}",
                        original_exception=e,
                        context={'output_file': output_file},
                        suggested_action="Check file permissions and disk space"
                    )
                    
                logger.info(f"处理完成，结果保存到: {output_file}")
            else:
                logger.info("处理结果:")
                logger.debug(result)  # Debug level to avoid large outputs

    @handle_layer_boundary(LayerType.UTILITY, "process_directory")
    def process_directory(self, input_dir: str, output_dir: str = None, delete_original: bool = False):
        """
        批量处理目录中的markdown文件
        """
        set_operation_context("process_directory")
        
        with error_boundary("directory processing", LayerType.UTILITY):
            # Validate inputs
            if not isinstance(input_dir, str):
                raise ValidationException(
                    message="Input directory must be a string",
                    field_name="input_dir",
                    details=f"Expected string, got {type(input_dir)}",
                    context={'input_type': type(input_dir).__name__},
                    suggested_action="Provide a valid directory path string"
                )
                
            if not input_dir:
                raise ValidationException(
                    message="Input directory cannot be empty",
                    field_name="input_dir",
                    details="Empty directory path provided",
                    suggested_action="Provide a non-empty directory path"
                )
                
            if output_dir is not None and not isinstance(output_dir, str):
                raise ValidationException(
                    message="Output directory must be a string or None",
                    field_name="output_dir",
                    details=f"Expected string or None, got {type(output_dir)}",
                    context={'input_type': type(output_dir).__name__},
                    suggested_action="Provide a valid output directory path or None"
                )
                
            if not isinstance(delete_original, bool):
                raise ValidationException(
                    message="Delete original must be a boolean",
                    field_name="delete_original",
                    details=f"Expected boolean, got {type(delete_original)}",
                    context={'input_type': type(delete_original).__name__},
                    suggested_action="Provide a boolean value for delete_original"
                )

            import glob
            
            logger.debug(f"Processing directory: {input_dir}")
            
            # Check directory existence
            if not os.path.exists(input_dir):
                raise FileProcessingException(
                    message="Input directory does not exist",
                    details=f"Directory not found: {input_dir}",
                    context={'directory_path': input_dir},
                    suggested_action="Verify directory path and ensure directory exists"
                )
                
            if not os.path.isdir(input_dir):
                raise ValidationException(
                    message="Input path is not a directory",
                    details=f"Path exists but is not a directory: {input_dir}",
                    context={'directory_path': input_dir},
                    suggested_action="Provide a valid directory path"
                )
            
            # 查找所有markdown文件
            pattern = os.path.join(input_dir, "*.md")
            md_files = glob.glob(pattern)
            
            if not md_files:
                logger.info(f"在目录 {input_dir} 中没有找到markdown文件")
                return
            
            # 确保输出目录存在
            if output_dir:
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except OSError as e:
                    raise FileProcessingException(
                        message="Failed to create output directory",
                        details=f"Directory creation error for {output_dir}: {str(e)}",
                        original_exception=e,
                        context={'directory_path': output_dir},
                        suggested_action="Check directory permissions and disk space"
                    )
        
            return self.process_files(md_files, output_dir, delete_original)

    @handle_layer_boundary(LayerType.UTILITY, "process_files")
    def process_files(self, md_files: List[str], output_dir: str = None, delete_original: bool = False):
        """
        批量处理多个markdown文件
        """
        set_operation_context("process_files")
        
        with error_boundary("batch file processing", LayerType.UTILITY):
            # Validate inputs
            if not isinstance(md_files, list):
                raise ValidationException(
                    message="Markdown files must be a list",
                    field_name="md_files",
                    details=f"Expected list, got {type(md_files)}",
                    context={'input_type': type(md_files).__name__},
                    suggested_action="Provide a list of file paths"
                )
                
            if output_dir is not None and not isinstance(output_dir, str):
                raise ValidationException(
                    message="Output directory must be a string or None",
                    field_name="output_dir",
                    details=f"Expected string or None, got {type(output_dir)}",
                    context={'input_type': type(output_dir).__name__},
                    suggested_action="Provide a valid output directory path or None"
                )
                
            if not isinstance(delete_original, bool):
                raise ValidationException(
                    message="Delete original must be a boolean",
                    field_name="delete_original",
                    details=f"Expected boolean, got {type(delete_original)}",
                    context={'input_type': type(delete_original).__name__},
                    suggested_action="Provide a boolean value for delete_original"
                )
                
            logger.debug(f"Processing {len(md_files)} files")
            output_files = []
            
            for input_file in md_files:
                if not isinstance(input_file, str):
                    logger.warning(f"Skipping non-string file path: {type(input_file)}")
                    continue
                    
                filename = os.path.basename(input_file)
                logger.info(f"处理文件: {filename}")

                with error_boundary(f"processing file {filename}", LayerType.UTILITY):
                    # Validate file path
                    if not input_file:
                        raise ValidationException(
                            message="File path cannot be empty",
                            field_name="input_file",
                            details="Empty file path provided",
                            suggested_action="Provide a valid file path"
                        )
                        
                    if not os.path.exists(input_file):
                        raise FileProcessingException(
                            message="Input file does not exist",
                            details=f"File not found: {input_file}",
                            context={'file_path': input_file},
                            suggested_action="Verify file path and ensure file exists"
                        )
                        
                    if not os.path.isfile(input_file):
                        raise ValidationException(
                            message="Input path is not a file",
                            details=f"Path exists but is not a file: {input_file}",
                            context={'file_path': input_file},
                            suggested_action="Provide a valid file path"
                        )

                    try:
                        with open(input_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                    except IOError as e:
                        raise FileProcessingException(
                            message="Failed to read input file",
                            details=f"File I/O error when reading {input_file}: {str(e)}",
                            original_exception=e,
                            context={'file_path': input_file},
                            suggested_action="Check file permissions and encoding"
                        )

                    try:
                        result = self.process_table(content, filename)
                    except Exception as e:
                        raise DataProcessingException(
                            message="Failed to process table content",
                            details=f"Error processing table from {input_file}: {str(e)}",
                            original_exception=e,
                            context={'file_path': input_file, 'content_length': len(content)},
                            suggested_action="Check file content format and structure"
                        )

                    # 确定输出文件路径
                    if output_dir:
                        output_file = os.path.join(output_dir, filename)
                    else:
                        # 在原目录生成，添加后缀
                        base_name = os.path.splitext(input_file)[0]
                        output_file = f"{base_name}_reformed.md"

                    # Extract and save metadata separately
                    try:
                        clean_content = self.extract_and_save_metadata(result, output_file)
                    except Exception as e:
                        raise DataProcessingException(
                            message="Failed to extract and save metadata",
                            details=f"Error extracting metadata for {output_file}: {str(e)}",
                            original_exception=e,
                            context={'output_file': output_file},
                            suggested_action="Check output file path and permissions"
                        )

                    # 写入结果
                    try:
                        with open(output_file, 'w', encoding='utf-8') as f:
                            f.write(clean_content)
                    except IOError as e:
                        raise FileProcessingException(
                            message="Failed to write output file",
                            details=f"File I/O error when writing {output_file}: {str(e)}",
                            original_exception=e,
                            context={'output_file': output_file},
                            suggested_action="Check file permissions and disk space"
                        )

                    logger.info(f"  -> 输出到: {output_file}")
                    output_files.append(output_file)

                    # 删除原文件（如果指定）
                    if delete_original:
                        try:
                            os.remove(input_file)
                            logger.info(f"  -> 已删除原文件: {input_file}")
                        except OSError as e:
                            logger.warning(f"Failed to delete original file {input_file}: {e}")

            return output_files
