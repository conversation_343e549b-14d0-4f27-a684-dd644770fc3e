"""
Crawler-Specific Exception Classes

This module extends the unified exception hierarchy from app.exceptions
with crawler-specific exception classes for web scraping, browser automation,
and data extraction operations.

Layer Architecture for Crawler:
- Presentation Layer: /crawler/streamlit_app.py
- Server Layer: /crawler/server.py  
- Core Layer: /crawler/core.py

The crawler follows the same unified error handling principles:
1. Boundary Exception Management
2. Exception Flow Control Prohibition
3. Intra-Layer Exception Propagation
4. Exception Wrapping Requirements
5. Try-Catch Minimization
"""

from typing import Optional, Dict, Any
from app.exceptions import (
    BaseAppException, LayerType, ErrorSeverity,
    PresentationLayerException, BusinessLogicException, UtilityException
)


# ============================================================================
# CRAWLER LAYER TYPE MAPPING
# ============================================================================

class CrawlerLayerType:
    """Mapping of crawler modules to unified layer types"""
    PRESENTATION = LayerType.PRESENTATION  # streamlit_app.py
    SERVER = LayerType.BUSINESS_LOGIC      # server.py (API/business logic)
    CORE = LayerType.UTILITY              # core.py (web scraping utilities)


# ============================================================================
# BASE CRAWLER EXCEPTIONS
# ============================================================================

class CrawlerException(BaseAppException):
    """Base exception for all crawler operations"""
    
    def __init__(self, message: str, error_code: str, layer: LayerType, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            layer=layer,
            **kwargs
        )


# ============================================================================
# PRESENTATION LAYER EXCEPTIONS (Streamlit App)
# ============================================================================

class CrawlerUIException(PresentationLayerException):
    """Streamlit UI related exceptions"""

    def __init__(self, message: str, **kwargs):
        # Set default suggested_action if not provided
        if 'suggested_action' not in kwargs:
            kwargs['suggested_action'] = "Please refresh the page and try again"

        super().__init__(
            message=message,
            error_code="CRAWLER_UI_001",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class UserInputException(PresentationLayerException):
    """User input validation exceptions in crawler UI"""
    
    def __init__(self, message: str, input_field: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_INPUT_001",
            severity=ErrorSeverity.LOW,
            **kwargs
        )
        if input_field:
            self.context = self.context or {}
            self.context['input_field'] = input_field


# ============================================================================
# SERVER LAYER EXCEPTIONS (API/Business Logic)
# ============================================================================

class CrawlerServerException(BusinessLogicException):
    """Base exception for crawler server operations"""
    
    def __init__(self, message: str, error_code: str, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            **kwargs
        )


class LoginException(CrawlerServerException):
    """Login and authentication related exceptions"""
    
    def __init__(self, message: str, login_status: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_LOGIN_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        if login_status:
            self.context = self.context or {}
            self.context['login_status'] = login_status


class AccountRestrictedException(LoginException):
    """Account permanently restricted or rate limited"""
    
    def __init__(self, message: str = "Account access restricted", restriction_type: str = "unknown", **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_ACCOUNT_001",
            severity=ErrorSeverity.CRITICAL,
            details=f"Account restriction type: {restriction_type}",
            suggested_action="Contact administrator for account review",
            **kwargs
        )
        self.context = self.context or {}
        self.context['restriction_type'] = restriction_type


class CrawlerSessionException(CrawlerServerException):
    """Session management related exceptions"""
    
    def __init__(self, message: str, session_id: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_SESSION_001",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        if session_id:
            self.context = self.context or {}
            self.context['session_id'] = session_id


# ============================================================================
# CORE LAYER EXCEPTIONS (Web Scraping/Browser Automation)
# ============================================================================

class WebScrapingException(UtilityException):
    """Base exception for web scraping operations"""
    
    def __init__(self, message: str, error_code: str, url: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            **kwargs
        )
        if url:
            self.context = self.context or {}
            self.context['url'] = url


class BrowserException(WebScrapingException):
    """Browser automation and control exceptions"""
    
    def __init__(self, message: str, browser_action: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_BROWSER_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        if browser_action:
            self.context = self.context or {}
            self.context['browser_action'] = browser_action


class PageLoadException(WebScrapingException):
    """Page loading and navigation exceptions"""
    
    def __init__(self, message: str, url: str, timeout: Optional[int] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_PAGE_001",
            severity=ErrorSeverity.MEDIUM,
            url=url,
            **kwargs
        )
        if timeout:
            self.context = self.context or {}
            self.context['timeout'] = timeout


class ElementNotFoundException(WebScrapingException):
    """Element selection and interaction exceptions"""
    
    def __init__(self, message: str, selector: str, url: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_ELEMENT_001",
            severity=ErrorSeverity.MEDIUM,
            url=url,
            **kwargs
        )
        self.context = self.context or {}
        self.context['selector'] = selector


class DataExtractionException(WebScrapingException):
    """Data extraction and parsing exceptions"""
    
    def __init__(self, message: str, data_type: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_DATA_001",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        if data_type:
            self.context = self.context or {}
            self.context['data_type'] = data_type


class SearchException(WebScrapingException):
    """Search operation related exceptions"""
    
    def __init__(self, message: str, keyword: str, search_type: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_SEARCH_001",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.context = self.context or {}
        self.context.update({
            'keyword': keyword,
            'search_type': search_type or 'unknown'
        })


class RateLimitException(WebScrapingException):
    """Rate limiting and throttling exceptions"""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_RATE_001",
            severity=ErrorSeverity.HIGH,
            suggested_action="Wait before retrying or reduce request frequency",
            **kwargs
        )
        if retry_after:
            self.context = self.context or {}
            self.context['retry_after'] = retry_after


class CaptchaException(WebScrapingException):
    """CAPTCHA and verification challenges"""
    
    def __init__(self, message: str = "CAPTCHA verification required", captcha_type: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_CAPTCHA_001",
            severity=ErrorSeverity.HIGH,
            suggested_action="Manual intervention required for CAPTCHA verification",
            **kwargs
        )
        if captcha_type:
            self.context = self.context or {}
            self.context['captcha_type'] = captcha_type


# ============================================================================
# NETWORK AND CONNECTION EXCEPTIONS
# ============================================================================

class NetworkException(WebScrapingException):
    """Network connectivity and HTTP related exceptions"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_NETWORK_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        if status_code:
            self.context = self.context or {}
            self.context['status_code'] = status_code


class TimeoutException(WebScrapingException):
    """Operation timeout exceptions"""
    
    def __init__(self, message: str, operation: str, timeout_duration: Optional[float] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CRAWLER_TIMEOUT_001",
            severity=ErrorSeverity.MEDIUM,
            suggested_action="Increase timeout duration or check network connectivity",
            **kwargs
        )
        self.context = self.context or {}
        self.context.update({
            'operation': operation,
            'timeout_duration': timeout_duration
        })




