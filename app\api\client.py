"""
API Client with Unified Error Handling Framework

This module provides client classes for interacting with the API services
with comprehensive error handling, logging, and debugging support.
"""

import requests
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

# Import unified error handling framework
from app.exceptions import (
    LayerType, ErrorSeverity, APIException, ValidationException,
    ExternalServiceException, DataProcessingException, ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for client operations
set_layer_context("api")

@dataclass
class ClientConfig:
    """Configuration for API clients"""
    base_url: str
    timeout: int = 120  # Default timeout for standard operations
    long_timeout: int = 600  # Extended timeout for long-running operations (10 minutes)
    verify_ssl: bool = True
    auth_token: Optional[str] = None

class BaseAPIClient:
    """Base API client with unified error handling"""
    
    def __init__(self, config: ClientConfig):
        self.config = config
        self.session = requests.Session()
        if config.auth_token:
            self.session.headers.update({"Authorization": f"Bearer {config.auth_token}"})
    
    @log_and_reraise(logger, "HTTP request execution")
    def _make_request(self, method: str, endpoint: str, use_long_timeout: bool = False, **kwargs) -> requests.Response:
        """Make HTTP request with unified error handling

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            use_long_timeout: Whether to use extended timeout for long-running operations
            **kwargs: Additional request parameters
        """
        set_operation_context(f"http_{method.lower()}_request")

        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        with error_boundary("HTTP request preparation", LayerType.API):
            # Set appropriate timeout based on operation type
            timeout = self.config.long_timeout if use_long_timeout else self.config.timeout
            kwargs.setdefault('timeout', timeout)
            kwargs.setdefault('verify', self.config.verify_ssl)

            logger.debug(f"Making {method} request to {url} (timeout: {timeout}s)")

        with error_boundary("HTTP request execution", LayerType.API):
            try:
                response = self.session.request(method, url, **kwargs)
                logger.debug(f"Response status: {response.status_code}")
                return response

            except requests.exceptions.Timeout as e:
                timeout_used = kwargs.get('timeout', timeout)
                raise ExternalServiceException(
                    message="Request timeout",
                    service_name="API Server",
                    details=f"Request to {url} timed out after {timeout_used} seconds",
                    original_exception=e,
                    context={'url': url, 'method': method, 'timeout': timeout_used, 'use_long_timeout': use_long_timeout},
                    suggested_action="Check network connectivity or increase timeout value"
                )
            except requests.exceptions.ConnectionError as e:
                raise ExternalServiceException(
                    message="Connection failed",
                    service_name="API Server",
                    details=f"Failed to connect to {url}: {str(e)}",
                    original_exception=e,
                    context={'url': url, 'method': method, 'base_url': self.config.base_url},
                    suggested_action="Check if the API server is running and accessible"
                )
            except requests.exceptions.RequestException as e:
                raise APIException(
                    message="HTTP request failed",
                    error_code="CLIENT_REQUEST_001",
                    details=f"Request error: {str(e)}",
                    original_exception=e,
                    context={'url': url, 'method': method},
                    suggested_action="Check request parameters and try again"
                )
    
    @log_and_reraise(logger, "API response processing")
    def _process_response(self, response: requests.Response) -> Dict[str, Any]:
        """Process API response with error handling"""
        set_operation_context("process_api_response")
        
        with error_boundary("response status validation", LayerType.API):
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                except ValueError:
                    error_data = {"detail": response.text}
                
                if response.status_code == 401:
                    raise APIException(
                        message="Authentication required",
                        error_code="CLIENT_AUTH_001",
                        http_status=401,
                        details=error_data.get("detail", "Invalid or expired token"),
                        context={'status_code': response.status_code, 'url': response.url},
                        suggested_action="Check authentication token and login again if needed"
                    )
                elif response.status_code == 403:
                    raise APIException(
                        message="Access forbidden",
                        error_code="CLIENT_AUTH_002",
                        http_status=403,
                        details=error_data.get("detail", "Insufficient permissions"),
                        context={'status_code': response.status_code, 'url': response.url},
                        suggested_action="Contact administrator for access permissions"
                    )
                elif response.status_code == 404:
                    raise APIException(
                        message="Resource not found",
                        error_code="CLIENT_NOT_FOUND_001",
                        http_status=404,
                        details=error_data.get("detail", "Requested resource does not exist"),
                        context={'status_code': response.status_code, 'url': response.url},
                        suggested_action="Verify the resource identifier and try again"
                    )
                else:
                    raise APIException(
                        message="API request failed",
                        error_code=f"CLIENT_HTTP_{response.status_code}",
                        http_status=response.status_code,
                        details=error_data.get("detail", f"HTTP {response.status_code} error"),
                        context={'status_code': response.status_code, 'url': response.url, 'response_data': error_data},
                        suggested_action="Check request parameters and server status"
                    )
        
        with error_boundary("response JSON parsing", LayerType.API):
            try:
                return response.json()
            except ValueError as e:
                raise DataProcessingException(
                    message="Invalid JSON response",
                    details=f"Failed to parse response as JSON: {str(e)}",
                    original_exception=e,
                    context={'status_code': response.status_code, 'content_type': response.headers.get('content-type')},
                    suggested_action="Check API server response format"
                )

class RAGWorkflowClient(BaseAPIClient):
    """Client for RAG workflow operations"""
    
    @handle_layer_boundary(LayerType.API, "RAG workflow execution")
    def execute_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute RAG workflow with unified error handling"""
        set_operation_context("execute_rag_workflow")
        
        with error_boundary("workflow data validation", LayerType.API):
            required_fields = ['query', 'context']
            missing_fields = [field for field in required_fields if field not in workflow_data]
            if missing_fields:
                raise ValidationException(
                    message="Missing required workflow fields",
                    field_name=missing_fields[0],
                    details=f"Required fields missing: {', '.join(missing_fields)}",
                    context={'provided_fields': list(workflow_data.keys()), 'required_fields': required_fields},
                    suggested_action=f"Include all required fields: {', '.join(required_fields)}"
                )
        
        with error_boundary("RAG workflow API call", LayerType.API):
            response = self._make_request('POST', '/rag/execute', json=workflow_data)
            result = self._process_response(response)
            
            logger.info(f"RAG workflow executed successfully")
            return result

class ReportAgentClient(BaseAPIClient):
    """Client for report agent operations"""
    
    @handle_layer_boundary(LayerType.API, "report agent session creation")
    def create_session(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new report agent session"""
        set_operation_context("create_agent_session")
        
        with error_boundary("agent session creation API call", LayerType.API):
            response = self._make_request('POST', '/reportagent/create', json={'config': config})
            result = self._process_response(response)
            
            logger.info(f"Report agent session created: {result.get('session_id')}")
            return result
    
    @handle_layer_boundary(LayerType.API, "report agent message processing")
    def process(self, session_id: str, user_input: str, **kwargs) -> Dict[str, Any]:
        """Process message through report agent"""
        set_operation_context("process_agent_message")
        
        with error_boundary("agent message processing API call", LayerType.API):
            # Ensure all string fields are valid strings, not None
            company = kwargs.get('company', '') or ''
            year = kwargs.get('year', '') or ''
            quarter = kwargs.get('quarter', '') or ''
            
            message_data = {
                'session_id': session_id,
                'user_input': user_input,
                'current_company': company,
                'current_year': year,
                'current_quarter': quarter,
                'extra_context': kwargs.get('extra_context', {})
            }
            
            # Use long timeout for report processing as it involves LLM calls and workflow execution
            response = self._make_request('POST', '/reportagent/process', use_long_timeout=True, json=message_data)
            result = self._process_response(response)

            # Backward compatibility: handle both old nested and new flat response structures
            # If the response has the old nested structure, flatten it for consistency
            if 'response' in result and isinstance(result['response'], dict):
                # Old structure: {"response": {...}, "agent_state": {...}, "session_id": "..."}
                # Flatten to: {..., "agent_state": {...}, "session_id": "..."}
                flattened_result = {**result['response']}
                if 'agent_state' in result:
                    flattened_result['agent_state'] = result['agent_state']
                if 'session_id' in result:
                    flattened_result['session_id'] = result['session_id']
                result = flattened_result
                logger.debug("Converted old nested response structure to flat structure for backward compatibility")

            logger.info(f"Message processed for session: {session_id}")
            return result
    
    @handle_layer_boundary(LayerType.API, "report scanning")
    def scan_reports(self, workspace_id: str) -> Dict[str, Any]:
        """Scan for available reports in workspace"""
        set_operation_context("scan_reports")
        
        with error_boundary("report scanning API call", LayerType.API):
            scan_data = {'workspace_id': workspace_id}
            response = self._make_request('POST', '/reportagent/scan', json=scan_data)
            result = self._process_response(response)
            
            logger.info(f"Reports scanned for workspace: {workspace_id}")
            return result

class TemplateClient(BaseAPIClient):
    """Client for template management operations"""
    
    @handle_layer_boundary(LayerType.API, "template retrieval")
    def get_templates(self) -> List[Dict[str, Any]]:
        """Get all available templates"""
        set_operation_context("get_templates")
        
        with error_boundary("template retrieval API call", LayerType.API):
            response = self._make_request('GET', '/templates')
            result = self._process_response(response)
            
            logger.info(f"Retrieved {len(result)} templates")
            return result
    
    @handle_layer_boundary(LayerType.API, "template creation")
    def create_template(self, name: str, content: str) -> Dict[str, Any]:
        """Create new template"""
        set_operation_context("create_template")
        
        with error_boundary("template data validation", LayerType.API):
            if not name or not name.strip():
                raise ValidationException(
                    message="Template name is required",
                    field_name="name",
                    details="name cannot be empty",
                    context={'name_length': len(name) if name else 0},
                    suggested_action="Provide a non-empty template name"
                )
            
            if not content:
                raise ValidationException(
                    message="Template content is required",
                    field_name="content",
                    details="content cannot be empty",
                    context={'content_length': len(content) if content else 0},
                    suggested_action="Provide template content"
                )
        
        with error_boundary("template creation API call", LayerType.API):
            template_data = {'name': name.strip(), 'content': content}
            response = self._make_request('POST', '/templates', json=template_data)
            result = self._process_response(response)
            
            logger.info(f"Template created: {name}")
            return result
    
    @handle_layer_boundary(LayerType.API, "template deletion")
    def delete_template(self, template_id: int) -> Dict[str, Any]:
        """Delete template"""
        set_operation_context("delete_template")
        
        with error_boundary("template deletion API call", LayerType.API):
            response = self._make_request('DELETE', f'/templates/{template_id}')
            result = self._process_response(response)
            
            logger.info(f"Template deleted: {template_id}")
            return result

class HealthClient(BaseAPIClient):
    """Client for health check operations"""
    
    @handle_layer_boundary(LayerType.API, "health check")
    def check_health(self) -> Dict[str, Any]:
        """Check API server health"""
        set_operation_context("health_check")
        
        with error_boundary("health check API call", LayerType.API):
            response = self._make_request('GET', '/health')
            result = self._process_response(response)
            
            logger.debug("Health check completed")
            return result

# Factory function for creating configured clients
@handle_layer_boundary(LayerType.API, "client factory")
def create_api_clients(base_url: str, auth_token: Optional[str] = None, **kwargs) -> Dict[str, BaseAPIClient]:
    """Create configured API clients"""
    set_operation_context("create_api_clients")
    
    with error_boundary("client configuration validation", LayerType.API):
        if not base_url:
            raise ConfigurationException(
                message="Base URL is required",
                details="base_url cannot be empty",
                context={'base_url': base_url},
                suggested_action="Provide a valid API base URL"
            )
        
        try:
            config = ClientConfig(
                base_url=base_url,
                auth_token=auth_token,
                **kwargs
            )
        except Exception as e:
            raise ConfigurationException(
                message="Invalid client configuration",
                details=f"Configuration creation failed: {str(e)}",
                original_exception=e,
                context={'base_url': base_url, 'kwargs': kwargs},
                suggested_action="Check configuration parameters"
            )
    
    with error_boundary("client instances creation", LayerType.API):
        try:
            clients = {
                'rag': RAGWorkflowClient(config),
                'report_agent': ReportAgentClient(config),
                'template': TemplateClient(config),
                'health': HealthClient(config)
            }
            
            logger.info(f"API clients created for: {base_url}")
            return clients
            
        except Exception as e:
            raise ConfigurationException(
                message="Failed to create API clients",
                details=f"Client instantiation failed: {str(e)}",
                original_exception=e,
                context={'base_url': base_url, 'client_types': ['rag', 'report_agent', 'template', 'health']},
                suggested_action="Check configuration and network connectivity"
            )
